import { useCallback } from 'react';
import { sharedAssetsCreationController } from '@controllers/assets';
import { routeController } from '@controllers/route';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import type { AssetResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedAssetFormModel } from '@models/assets';
import { useNavigate } from '@remix-run/react';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';

const FORM_ID = 'addAssetForm';
const VIEW_ID = 'addAsset';

export const AddAssetFormComponent = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { schema } = sharedAssetFormModel;
    const { isCreating } = sharedAssetsCreationController;
    const navigate = useNavigate();

    const handleCancelClick = () => {
        navigate(`${routeController.userPartOfUrl}/risk/assets`);
    };

    const handleSaveClick = useCallback(() => {
        triggerSubmit().catch(() => {
            console.error('Failed to submit form');
        });
    }, [triggerSubmit]);

    const handleFormSubmit = useCallback(
        (values: FormValues) => {
            sharedAssetsCreationController.createAsset(
                values,
                (createdAsset: AssetResponseDto) => {
                    // Navigate to asset details page on success
                    const currentPath = routeController.userPartOfUrl;

                    navigate(`${currentPath}/risk/assets/${createdAsset.id}`);
                },
            );
        },
        [navigate],
    );

    return (
        <Stack gap="md" direction="column" data-id="lKknzwTB">
            <Form
                hasExternalSubmitButton
                ref={formRef}
                data-id={`${VIEW_ID}-${FORM_ID}`}
                formId={FORM_ID}
                schema={schema}
                onSubmit={handleFormSubmit}
            />
            <Stack gap="md" direction="row" align="end" width="100%" pt="xl">
                <Button
                    type="button"
                    label={t`Save asset`}
                    colorScheme="primary"
                    data-id={`${VIEW_ID}-${FORM_ID}-submitButton`}
                    isLoading={isCreating}
                    onClick={handleSaveClick}
                />
                <Button
                    type="button"
                    label={t`Cancel`}
                    level="secondary"
                    data-id={`${VIEW_ID}-${FORM_ID}-cancelButton`}
                    onClick={handleCancelClick}
                />
            </Stack>
        </Stack>
    );
});
