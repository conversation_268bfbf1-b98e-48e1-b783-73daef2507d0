import type { VendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type {
    VendorQuestionnaireCategoryType,
    VendorQuestionnaireResponseType,
    VendorQuestionnaireShortAnswerType,
    VendorQuestionnaireStatus,
} from '../types/vendor-questionnaires.type';

export function getVendorRenewalScheduleLabel(
    renewalScheduleType: NonNullable<VendorResponseDto['renewalScheduleType']>,
): string {
    switch (renewalScheduleType) {
        case 'ONE_MONTH': {
            return t`1 Month`;
        }
        case 'TWO_MONTHS': {
            return t`2 Months`;
        }
        case 'THREE_MONTHS': {
            return t`3 Months`;
        }
        case 'SIX_MONTHS': {
            return t`6 Months`;
        }
        case 'ONE_YEAR': {
            return t`1 Year`;
        }
        case 'CUSTOM': {
            return t`Custom`;
        }
        case 'NONE': {
            return t`None`;
        }
        default: {
            return t`Unknown`;
        }
    }
}

export function getVendorRiskLabel(risk: VendorResponseDto['risk']): string {
    switch (risk) {
        case 'NONE': {
            return t`None`;
        }
        case 'LOW': {
            return t`Low`;
        }
        case 'MODERATE': {
            return t`Moderate`;
        }
        case 'HIGH': {
            return t`High`;
        }
        default: {
            return t`Unknown`;
        }
    }
}

export function getVendorCategoryLabelsRecord(): Record<
    VendorQuestionnaireCategoryType,
    string
> {
    return {
        ADMINISTRATIVE: t`Administrative`,
        CS: t`Customer Success`,
        ENGINEERING: t`Engineering`,
        FINANCE: t`Finance`,
        HR: t`Human Resources`,
        INFORMATION_TECHNOLOGY: t`Information Technology`,
        LEGAL: t`Legal`,
        MARKETING: t`Marketing`,
        PRODUCT: t`Product`,
        SALES: t`Sales`,
        SECURITY: t`Security`,
        NONE: t`None`,
    };
}

export function getResponseTypeLabel(
    responseType: VendorQuestionnaireResponseType,
): string {
    switch (responseType) {
        case 'SHORT_ANSWER': {
            return t`Short Answer`;
        }
        case 'LONG_ANSWER': {
            return t`Long Answer`;
        }
        case 'MULTIPLE_CHOICE': {
            return t`Multiple Choice`;
        }
        case 'CHECKBOXES': {
            return t`Checkboxes`;
        }
        case 'YES_NO': {
            return t`Yes/No`;
        }
        case 'DATE': {
            return t`Date`;
        }
        case 'FILE_UPLOAD': {
            return t`File Upload`;
        }
        default: {
            return t`Unknown`;
        }
    }
}

export function getShortAnswerTypeLabel(
    shortAnswerType: VendorQuestionnaireShortAnswerType,
): string {
    switch (shortAnswerType) {
        case 'TEXT': {
            return t`Text`;
        }
        case 'EMAIL': {
            return t`Email`;
        }
        case 'URL': {
            return t`Website URL`;
        }
        case 'PHONE': {
            return t`Phone Number`;
        }
        default: {
            return t`Unknown`;
        }
    }
}

export function getVendorQuestionnaireStatusLabel(
    status: VendorQuestionnaireStatus,
): string {
    switch (status) {
        case 'DRAFT': {
            return t`Draft`;
        }
        case 'ACTIVE': {
            return t`Active`;
        }
        case 'DEFAULT': {
            return t`Active`;
        }
        default: {
            return t`Unknown`;
        }
    }
}
