import { Metadata } from '@cosmos/components/metadata';
import { QUESTIONNAIRE_STATUS_COLOR_SCHEMES } from './constants/vendor-questionnaires.constant';
import { getVendorQuestionnaireStatusLabel } from './helpers/vendor-labels.helper';
import type { VendorQuestionnaireCellStatusProps } from './types/vendor-questionnaires.type';

export const VendorsQuestionnairesCellStatusComponent = ({
    row,
}: VendorQuestionnaireCellStatusProps): React.JSX.Element => {
    const { status } = row.original;

    return (
        <Metadata
            label={getVendorQuestionnaireStatusLabel(status)}
            colorScheme={QUESTIONNAIRE_STATUS_COLOR_SCHEMES[status]}
            data-testid="VendorsQuestionnairesCellStatusComponent"
            data-id="5VYFzRs5"
        />
    );
};
