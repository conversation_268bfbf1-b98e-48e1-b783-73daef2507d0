import { DateTime } from '@cosmos-lab/components/date-time';
import type { VendorQuestionnaireRowData } from './types/vendor-questionnaires.type';

export interface VendorQuestionnaireCellLastUsedProps {
    row: {
        original: VendorQuestionnaireRowData;
    };
}

export const VendorsQuestionnairesCellLastUsedComponent = ({
    row,
}: VendorQuestionnaireCellLastUsedProps): React.JSX.Element | null => {
    const { updatedAt } = row.original;

    if (!updatedAt) {
        return null;
    }

    return (
        <DateTime
            date={updatedAt}
            format="sentence_time"
            data-testid="VendorsQuestionnairesCellLastUsedComponent"
            data-id="8mK3nP9Q"
        />
    );
};
