import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { dimension48x, dimension72x } from '@cosmos/constants/tokens';
import { getVendorCategoryLabelsRecord } from './helpers/vendor-labels.helper';
import type { VendorQuestionnaireCategoryType } from './types/vendor-questionnaires.type';

interface CategoriesPopoverContentProps {
    categories: VendorQuestionnaireCategoryType[];
}

export const CategoriesPopoverContent = ({
    categories,
}: CategoriesPopoverContentProps): React.JSX.Element => {
    const categoryLabels = getVendorCategoryLabelsRecord();

    return (
        <Stack
            gap="1x"
            direction="row"
            wrap="wrap"
            maxWidth={dimension72x}
            minWidth={dimension48x}
            data-testid="CategoriesPopoverContent"
            data-id="J_8c2Ktx"
        >
            {categories.map((category) => (
                <Metadata
                    key={category}
                    label={categoryLabels[category]}
                    colorScheme="neutral"
                    data-id={`popover-category-${category}`}
                />
            ))}
        </Stack>
    );
};
