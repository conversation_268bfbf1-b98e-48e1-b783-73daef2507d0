import { noop } from 'lodash-es';
import { Button } from '@cosmos/components/button';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { FORM_ID } from './constants/vendor-questionnaires.constant';
import type { VendorQuestionnaireFieldChoice } from './types/vendor-questionnaires.type';
import { VendorQuestionnairesQuestionChoiceListComponent } from './vendor-questionnaires-question-choice-list-component';

export const VendorQuestionnairesQuestionChoicesComponent = ({
    choices,
    allowOtherChoice,
}: {
    choices: VendorQuestionnaireFieldChoice[];
    allowOtherChoice: boolean;
}): React.JSX.Element => {
    return (
        <Grid
            gap="6x"
            data-testid="VendorQuestionnairesQuestionChoicesComponent"
            data-id="0biNtLcY"
        >
            <CheckboxField
                aria-labelledby={undefined}
                data-id={`${FORM_ID}-addCustomResponse`}
                formId={FORM_ID}
                label={t`Let people add a custom response`}
                name="addCustomResponse"
                value={String(allowOtherChoice)}
                checked={allowOtherChoice}
                onChange={noop}
            />

            <VendorQuestionnairesQuestionChoiceListComponent
                choices={choices}
                onDelete={noop}
            />

            <Stack align="center" justify="start">
                <Button
                    data-id={`${FORM_ID}-addOption`}
                    label={t`Add option`}
                    startIconName="Plus"
                    level="tertiary"
                    onClick={noop}
                />
            </Stack>
        </Grid>
    );
};
