import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import {
    QUESTIONNAIRE_RISK_LEVELS_COLOR_SCHEMES,
    VENDOR_RISK_LIST_OPTION,
} from './constants/vendor-questionnaires.constant';
import { getVendorRiskLabel } from './helpers/vendor-labels.helper';
import type { VendorQuestionnaireCellRiskLevelProps } from './types/vendor-questionnaires.type';

export const VendorsQuestionnairesCellRiskLevelComponent = ({
    row,
}: VendorQuestionnaireCellRiskLevelProps): React.JSX.Element => {
    const { riskLevels } = row.original;

    // Check if all risk levels are selected
    if (riskLevels.length === VENDOR_RISK_LIST_OPTION.length) {
        return (
            <Text
                data-testid="VendorsQuestionnairesCellRiskLevelComponent"
                data-id="2zb9wp6A"
            >
                {t`All Risk Levels`}
            </Text>
        );
    }

    return (
        <Stack
            gap="3x"
            direction="column"
            data-testid="VendorsQuestionnairesCellRiskLevelComponent"
            data-id="2zb9wp6A"
        >
            {riskLevels.map((riskLevel) => {
                return (
                    <Metadata
                        key={riskLevel}
                        label={getVendorRiskLabel(riskLevel)}
                        data-id="bwiJfoQy"
                        colorScheme={
                            QUESTIONNAIRE_RISK_LEVELS_COLOR_SCHEMES[riskLevel]
                        }
                    />
                );
            })}
        </Stack>
    );
};
