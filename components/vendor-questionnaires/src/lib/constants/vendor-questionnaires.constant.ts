import type { CheckboxOption } from '@cosmos/components/checkbox-field-group';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { ColorScheme } from '@cosmos/components/metadata';
import {
    getResponseTypeLabel,
    getShortAnswerTypeLabel,
    getVendorCategoryLabelsRecord,
    getVendorQuestionnaireStatusLabel,
    getVendorRiskLabel,
} from '../helpers/vendor-labels.helper';
import type {
    VendorQuestionnaire<PERSON>ield,
    VendorQuestionnaireFieldChoice,
    VendorQuestionnaireResponseType,
    VendorQuestionnaireRiskType,
    VendorQuestionnaireShortAnswerType,
    VendorQuestionnaireStatus,
} from '../types/vendor-questionnaires.type';

export const FORM_ID = 'vendor-questionnaire-form';

const RESPONSE_TYPE_VALUES: VendorQuestionnaireResponseType[] = [
    'SHORT_ANSWER',
    'LONG_ANSWER',
    'MULTIPLE_CHOICE',
    'CHECKBOXES',
    'YES_NO',
    'DATE',
    'FILE_UPLOAD',
];

export const RESPONSE_TYPE_OPTIONS: ListBoxItemData[] =
    RESPONSE_TYPE_VALUES.map((value, index) => {
        return {
            id: `${value}-${index}`,
            label: getResponseTypeLabel(value),
            value,
        };
    });

const SHORT_ANSWER_TYPE_VALUES: VendorQuestionnaireShortAnswerType[] = [
    'TEXT',
    'EMAIL',
    'URL',
    'PHONE',
];

export const SHORT_ANSWER_TYPE_OPTIONS: ListBoxItemData[] =
    SHORT_ANSWER_TYPE_VALUES.map((value, index) => {
        return {
            id: `${value}-${index}`,
            label: getShortAnswerTypeLabel(value),
            value,
        };
    });

const VENDOR_RISK_VALUES: VendorQuestionnaireRiskType[] = [
    'NONE',
    'LOW',
    'MODERATE',
    'HIGH',
];

const VENDOR_QUESTIONNAIRE_STATUS_VALUES: VendorQuestionnaireStatus[] = [
    'DRAFT',
    'ACTIVE',
];

export const VENDOR_QUESTIONNAIRE_STATUS_OPTIONS: ListBoxItemData[] =
    VENDOR_QUESTIONNAIRE_STATUS_VALUES.map((value) => {
        return {
            id: value,
            label: getVendorQuestionnaireStatusLabel(value),
            value,
        };
    });

export const VENDOR_QUESTIONNAIRE_CATEGORY_CHECKBOX_OPTIONS: CheckboxOption[] =
    Object.entries(getVendorCategoryLabelsRecord()).map(([value, label]) => {
        return {
            id: value,
            label,
            value,
        };
    });

export const VENDOR_QUESTIONNAIRE_CATEGORY_OPTIONS: ListBoxItemData[] =
    Object.entries(getVendorCategoryLabelsRecord()).map(([value, label]) => {
        return {
            id: value,
            label,
            value,
        };
    });

export const VENDOR_RISK_OPTIONS: CheckboxOption[] = VENDOR_RISK_VALUES.map(
    (value) => {
        return {
            id: value,
            label: getVendorRiskLabel(value),
            value,
        };
    },
);

export const VENDOR_RISK_LIST_OPTION: ListBoxItemData[] =
    VENDOR_RISK_VALUES.map((value) => {
        return {
            id: value,
            label: getVendorRiskLabel(value),
            value,
        };
    });

export const QUESTIONNAIRE_STATUS_COLOR_SCHEMES: Readonly<
    Record<VendorQuestionnaireStatus, ColorScheme>
> = {
    DRAFT: 'neutral',
    ACTIVE: 'success',
    DEFAULT: 'success',
};

export const QUESTIONNAIRE_RISK_LEVELS_COLOR_SCHEMES: Readonly<
    Record<VendorQuestionnaireRiskType, ColorScheme>
> = {
    NONE: 'neutral',
    LOW: 'success',
    MODERATE: 'warning',
    HIGH: 'critical',
};

export const MAX_CATEGORIES_TO_SHOW = 2;

/**
 * Const added for testing purposes.
 */
export const VENDOR_NEW_QUESTIONNAIRE = {
    ref: `Q${Date.now()}`,
    title: '',
    type: 'short_answer',
    shortAnswerType: 'short_text',
    includeFollowUpQn: false,
    followUpQnTrigger: true,
    followUpQn: '',
    allowOtherChoice: false,
    choices: [],
    required: false,
} as const satisfies VendorQuestionnaireField;

/**
 * Const added for testing purposes.
 */
export const VENDOR_QUESTIONNAIRE_NEW_CHOICE = {
    ref: `C${Date.now()}`,
    label: '',
} as const satisfies VendorQuestionnaireFieldChoice;
