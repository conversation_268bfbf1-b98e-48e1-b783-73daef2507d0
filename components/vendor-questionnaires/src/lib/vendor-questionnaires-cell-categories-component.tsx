import { useRef, useState } from 'react';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { CategoriesPopoverContent } from './categories-popover-content.component';
import { MAX_CATEGORIES_TO_SHOW } from './constants/vendor-questionnaires.constant';
import { getVendorCategoryLabelsRecord } from './helpers/vendor-labels.helper';
import type { VendorQuestionnaireCellCategoriesProps } from './types/vendor-questionnaires.type';

export const VendorsQuestionnairesCellCategoriesComponent = ({
    row,
}: VendorQuestionnaireCellCategoriesProps): React.JSX.Element => {
    const { categories } = row.original;
    const categoryLabels = getVendorCategoryLabelsRecord();
    const remaining = categories.length - MAX_CATEGORIES_TO_SHOW;

    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);

    const categoriesToShow =
        categories.length > MAX_CATEGORIES_TO_SHOW
            ? categories.slice(0, MAX_CATEGORIES_TO_SHOW)
            : categories;

    const remainingCategories = categories.slice(MAX_CATEGORIES_TO_SHOW);

    const handleClick = () => {
        setIsPopoverOpen(!isPopoverOpen);
    };

    return (
        <Stack
            gap="2x"
            direction="row"
            wrap="wrap"
            data-testid="VendorsQuestionnairesCellCategoriesComponent"
            data-id="lAIorDKb"
        >
            {categoriesToShow.map((category) => (
                <Metadata
                    key={category}
                    label={categoryLabels[category]}
                    colorScheme="neutral"
                    data-id={`category-${category}`}
                />
            ))}
            {remaining > 0 && (
                <>
                    <Button
                        ref={buttonRef}
                        colorScheme="neutral"
                        data-id="categories-popover-trigger"
                        label={`+${remaining}`}
                        level="tertiary"
                        size="sm"
                        type="button"
                        onClick={handleClick}
                    />
                    <Popover
                        anchor={buttonRef.current}
                        isOpen={isPopoverOpen}
                        placement="top"
                        padding="xl"
                        data-id="categories-popover"
                        content={
                            <CategoriesPopoverContent
                                categories={remainingCategories}
                            />
                        }
                        onDismiss={() => {
                            setIsPopoverOpen(false);
                        }}
                    />
                </>
            )}
        </Stack>
    );
};
