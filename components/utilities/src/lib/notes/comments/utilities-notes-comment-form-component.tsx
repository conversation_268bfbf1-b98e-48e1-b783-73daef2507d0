import { isEmpty } from 'lodash-es';
import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import type {
    CosmosFileObject,
    SupportedFormat,
} from '@cosmos/components/file-upload';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type { NoteFileResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    type FormSchema,
    FormWrapper,
    UniversalFormField,
    useFormSubmit,
} from '@ui/forms';
import { AttachmentFormModalContent } from './attachment-form-modal-content.component';
import { UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID } from './utilities-notes-comment-attachment-form-constant';

const formId = 'NoteCommentForm';
const MAX_SOURCE_CHARACTERS = 768;
/**
 * 5MB.
 */
const MAX_FILE_SIZE_IN_BYTES = 1024 * 1024 * 5;

const getUtilitiesNotesCommentFormSchema = ({
    comment,
    attachments,
    maxNoteCharacters,
    source,
    hasSource,
    attachmentConfig = {},
    commentLabel = t`New note`,
    sourceLabel = t`Source`,
}: {
    comment: string;
    attachments?: NoteFileResponseDto[];
    maxNoteCharacters: number;
    source: string;
    hasSource: boolean;
    attachmentConfig?: {
        showAddAttachment?: 'modal' | 'field';
        acceptedFormats?: SupportedFormat[];
    };
    commentLabel?: string;
    sourceLabel?: string;
}): FormSchema => ({
    comment: {
        type: 'textarea',
        maxCharacters: maxNoteCharacters,
        initialValue: comment,
        label: commentLabel,
        rows: 5,
        labelStyleOverrides: {
            size: 'sm',
        },
    },
    ...(attachmentConfig.showAddAttachment === 'field'
        ? {
              files: {
                  type: 'file',
                  label: t`Attachments`,
                  isOptional: true,
                  initialValue:
                      attachments?.map((a) => ({
                          // TODO: needs work here [ENG-63732]
                          file: new File([], a.name),
                          errors: [],
                      })) ?? [],
                  maxFileSizeInBytes: MAX_FILE_SIZE_IN_BYTES,
                  selectButtonText: t`Select file`,
                  removeButtonText: t`Remove file`,
                  acceptedFormats: attachmentConfig.acceptedFormats ?? [
                      'jpeg',
                      'png',
                  ],
                  oneFileOnly: false,
                  errorCodeMessages: {
                      'file-invalid-type': t`Not a valid file type.`,
                      'file-too-large': t`File size is too large.`,
                      'file-too-small': t`File size is too small.`,
                      'too-many-files': t`Contains too many files.`,
                  },
                  innerLabel: t`Or drop files here`,
              },
          }
        : null),
    ...(hasSource
        ? {
              source: {
                  type: 'textarea',
                  isOptional: true,
                  maxCharacters: MAX_SOURCE_CHARACTERS,
                  label: sourceLabel,
                  initialValue: source,
              },
          }
        : null),
});

interface NoteAttachment {
    name: string;
    creationDate: string;
    file: CosmosFileObject;
}

export const NoteCommentForm = ({
    comment,
    attachments,
    maxNoteCharacters,
    source,
    hasSource,
    editMode,
    onSubmit,
    onCancel,
    commentLabel,
    sourceLabel,
    labels = {},
    attachmentConfig = {},
}: {
    comment: string;
    attachments: NoteFileResponseDto[];
    maxNoteCharacters: number;
    source: string;
    hasSource: boolean;
    editMode: boolean;
    commentLabel?: string;
    sourceLabel?: string;
    labels?: {
        confirmationButton?: string;
    };
    attachmentConfig?: {
        showAddAttachment?: 'modal' | 'field';
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
    };
    onChange?: (hasChanges: boolean) => void;
    onSubmit: ({
        comment,
        source,
        files,
        deletedAttachmentIds,
    }: {
        comment: string;
        source: string;
        files: {
            name: string;
            creationDate: string;
            file: CosmosFileObject;
        }[];
        deletedAttachmentIds: string[];
    }) => void;
    onCancel?: (
        getCurrentValues?: () => { comment: string; source: string },
    ) => void;
}): React.JSX.Element => {
    const schema = useMemo(
        () =>
            getUtilitiesNotesCommentFormSchema({
                comment,
                attachments,
                maxNoteCharacters,
                source,
                hasSource,
                attachmentConfig,
                commentLabel,
                sourceLabel,
            }),
        [
            comment,
            attachments,
            maxNoteCharacters,
            source,
            hasSource,
            commentLabel,
            sourceLabel,
            attachmentConfig,
        ],
    );

    const [files, setFiles] = useState<NoteAttachment[]>([]);
    const [deletedAttachmentIds, setDeletedAttachmentIds] = useState<string[]>(
        [],
    );

    // Compute existing attachments, filtering out deleted ones
    const existingAttachments = useMemo(
        () =>
            attachments.filter((att) => !deletedAttachmentIds.includes(att.id)),
        [attachments, deletedAttachmentIds],
    );

    const getDefaultActionLabel = () => {
        if (editMode) {
            return t`Save note`;
        }

        return t`Add note`;
    };

    const actionButtonLabel =
        labels.confirmationButton ?? getDefaultActionLabel();

    const handleDeleteExistingAttachment = useCallback(
        (attachmentId: string) => {
            setDeletedAttachmentIds((prev) => [...prev, attachmentId]);
        },
        [],
    );

    const modalContent = useCallback(
        () => (
            <AttachmentFormModalContent
                data-id="nM7RrUmB"
                attachmentConfig={attachmentConfig}
                onSubmit={(attachment) => {
                    if (isEmpty(attachment)) {
                        return;
                    }
                    const newFile: CosmosFileObject = {
                        file: attachment.file,
                        errors: [],
                    };

                    const updatedFiles: NoteAttachment[] = [
                        ...files,
                        { ...attachment, file: newFile },
                    ];

                    setFiles(updatedFiles);
                }}
            />
        ),
        [files, attachmentConfig],
    );

    const { formRef, triggerClearForm, getFormValues } = useFormSubmit();

    const getCurrentFormValues = useCallback(() => {
        const values = getFormValues();

        if (values) {
            return {
                comment: (values.comment as string) || '',
                source: (values.source as string) || '',
            };
        }

        return { comment: '', source: '' };
    }, [getFormValues]);

    return (
        <FormWrapper
            schema={schema}
            data-testid="NoteCommentForm"
            data-id="iGWF_VL5"
            ref={formRef}
            formId={formId}
            onSubmit={({
                comment: newComment,
                source: newSource,
                files: filesFromField = [],
            }) => {
                const filesFormattedFromField =
                    attachmentConfig.showAddAttachment === 'field'
                        ? (filesFromField as File[]).map((f) => ({
                              name: f.name,
                              creationDate: f.lastModified.toString(),
                              file: { file: f, errors: [] },
                          }))
                        : [];

                onSubmit({
                    comment: newComment as string,
                    source: newSource as string,
                    files:
                        attachmentConfig.showAddAttachment === 'modal'
                            ? files
                            : filesFormattedFromField,
                    deletedAttachmentIds,
                });
                triggerClearForm();
                setFiles([]);
                setDeletedAttachmentIds([]);
            }}
        >
            <UniversalFormField
                name="comment"
                showDivider={false}
                formId={formId}
                data-id="comment-field"
                labelStyleOverrides={{
                    size: 'md',
                    type: 'title',
                }}
            />
            {hasSource && (
                <UniversalFormField
                    name="source"
                    showDivider={false}
                    formId={formId}
                    data-id="source-field"
                    labelStyleOverrides={{
                        size: 'md',
                        type: 'title',
                    }}
                />
            )}
            {attachmentConfig.showAddAttachment === 'field' && (
                <UniversalFormField
                    name="files"
                    showDivider={false}
                    formId={formId}
                    data-id="files-field"
                    labelStyleOverrides={{
                        size: 'md',
                        type: 'title',
                    }}
                />
            )}

            {editMode && !isEmpty(existingAttachments) && (
                <Box pt="3x">
                    <StackedList aria-label={t`Existing attachments`}>
                        {existingAttachments.map((attachment) => (
                            <StackedListItem
                                key={`existing-attachment-${attachment.id}`}
                                data-id={`existing-attachment-${attachment.id}`}
                                primaryColumn={
                                    <Text
                                        size="200"
                                        type="body"
                                        colorScheme="neutral"
                                    >
                                        {attachment.name}
                                    </Text>
                                }
                                secondaryColumn={
                                    <DateTime
                                        date={attachment.createdAt}
                                        format="table_time"
                                        textProps={{
                                            type: 'body',
                                            size: '100',
                                            colorScheme: 'faded',
                                        }}
                                    />
                                }
                                action={
                                    <Button
                                        isIconOnly
                                        startIconName="Trash"
                                        label={t`Remove attachment`}
                                        type="button"
                                        level="tertiary"
                                        colorScheme="danger"
                                        size="sm"
                                        data-id={`remove-attachment-${attachment.id}`}
                                        onClick={() => {
                                            handleDeleteExistingAttachment(
                                                attachment.id,
                                            );
                                        }}
                                    />
                                }
                            />
                        ))}
                    </StackedList>
                </Box>
            )}

            {/* Display newly added files */}
            {!isEmpty(files) && (
                <Box pt="3x">
                    <StackedList aria-label={t`New attachments`}>
                        {files.map((file, index) => (
                            <StackedListItem
                                key={`new-attachment-${file.name}`}
                                data-id={`new-attachment-${file.name}`}
                                primaryColumn={
                                    <Text
                                        size="200"
                                        type="title"
                                        data-id={`new-attachment-name-${file.name}`}
                                    >
                                        {file.name}
                                    </Text>
                                }
                                secondaryColumn={
                                    <Text
                                        size="100"
                                        colorScheme="faded"
                                        data-id={`new-attachment-date-${file.name}`}
                                    >
                                        <DateTime
                                            date={file.creationDate}
                                            format="table_time"
                                        />
                                    </Text>
                                }
                                action={
                                    <Button
                                        isIconOnly
                                        startIconName="Trash"
                                        label={t`Remove attachment`}
                                        type="button"
                                        level="tertiary"
                                        colorScheme="danger"
                                        size="sm"
                                        data-id={`remove-new-attachment-${file.name}`}
                                        onClick={() => {
                                            setFiles((prev) =>
                                                prev.filter(
                                                    (_, i) => i !== index,
                                                ),
                                            );
                                        }}
                                    />
                                }
                            />
                        ))}
                    </StackedList>
                </Box>
            )}

            {attachmentConfig.showAddAttachment === 'modal' && (
                <Stack direction="row" gap="md" justify="start" pb="sm">
                    <Button
                        hasPadding={false}
                        label={t`Add attachment`}
                        level="tertiary"
                        data-id="latest-run-button"
                        onClick={() => {
                            modalController.openModal({
                                id: UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
                                content: modalContent,
                                size: 'lg',
                                centered: true,
                                disableClickOutsideToClose: true,
                            });
                        }}
                    />
                </Stack>
            )}

            <Stack direction="row" gap="md" justify="start">
                <Button
                    type="submit"
                    label={actionButtonLabel}
                    data-id="latest-run-button"
                />
                {editMode ? (
                    <Button
                        type="button"
                        label={t`Cancel`}
                        level="secondary"
                        data-id="latest-run-button"
                        onClick={() => onCancel?.(getCurrentFormValues)}
                    />
                ) : null}
            </Stack>
        </FormWrapper>
    );
};
