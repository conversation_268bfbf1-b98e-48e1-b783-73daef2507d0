import { isEmpty } from 'lodash-es';
import { useMemo, useState } from 'react';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import {
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { DateTime } from '@cosmos-lab/components/date-time';
import { Divider } from '@cosmos-lab/components/divider';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { StatusDot } from '@cosmos-lab/components/status-dot';
import { t } from '@globals/i18n/macro';
import { getInitials } from '@helpers/formatters';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { NoteUpdateDto } from '../utilities-notes-create-dto-types';
import { NoteCommentForm } from './utilities-notes-comment-form-component';
import type { CommentProps } from './utilities-notes-comment-types';

// Main props interface that supports both notes and messages
export interface CommentComponentProps extends CommentProps {
    enableReadStatusTracking?: boolean;
    hasBeenRead?: boolean;
    maxNoteCharacters?: number;
    canEdit?: boolean;
    onUpdateReadStatus?: (id: string, hasBeenRead: boolean) => void;
    defaultEditLabel?: string;
    defaultDeleteLabel?: string;
}

const MAX_SOURCE_CHARACTERS = 768;

/**
 * Main component that handles both notes and messages.
 */
export const UtilitiesCommentComponent = ({
    id,
    value,
    createdAt,
    identityName,
    imgSrc,
    maxNoteCharacters = MAX_SOURCE_CHARACTERS,
    attachments = [],
    hasDivider = true,
    isReadOnly = false,
    hasSecondaryInput = false,
    secondaryInputLabel = 'Source',
    secondaryInputValue = '',
    commentFieldLabel,
    sourceFieldLabel,
    labels = {},
    attachmentConfig = {},
    enableReadStatusTracking = false,
    hasBeenRead = true,
    canEdit = true,
    defaultEditLabel = t`Edit note`,
    defaultDeleteLabel = t`Delete note`,
    onSave,
    onDelete,
    onDownloadAttachment,
    onUpdateReadStatus,
}: CommentComponentProps): React.JSX.Element => {
    const [isEditable, setIsEditable] = useState(false);

    // Use provided labels or defaults
    const finalEditTooltipLabel = labels.editTooltip ?? defaultEditLabel;
    const finalDeleteTooltipLabel = labels.deleteTooltip ?? defaultDeleteLabel;
    const finalConfirmationButtonLabel = labels.confirmationButton;
    const finalCancelConfirmationBody =
        labels.cancelConfirmationBody ??
        t`If you leave now, your note changes will be lost.`;

    const dropdownItems: SchemaDropdownItems = useMemo(() => {
        // For read-only messages, show only mark as read/unread option
        if (isReadOnly && enableReadStatusTracking && onUpdateReadStatus) {
            const readLabel = hasBeenRead ? t`Mark as unread` : t`Mark as read`;

            return [
                {
                    id: `${id}-mark-read-option`,
                    label: readLabel,
                    onSelect: () => {
                        onUpdateReadStatus(id, !hasBeenRead);
                    },
                },
            ];
        }

        // For editable items, show edit and delete options
        return [
            {
                id: `${id}-edit-option`,
                label: finalEditTooltipLabel,
                onSelect: () => {
                    setIsEditable(true);
                },
            },
            {
                id: `${id}-delete-option`,
                label: finalDeleteTooltipLabel,
                colorScheme: 'critical',
                onSelect: () => {
                    onDelete(id);
                },
            },
        ];
    }, [
        id,
        isReadOnly,
        enableReadStatusTracking,
        hasBeenRead,
        onUpdateReadStatus,
        onDelete,
        finalEditTooltipLabel,
        finalDeleteTooltipLabel,
    ]);

    return (
        <Box
            borderRadius="borderRadiusLg"
            p="sm"
            data-id="B8dbdLlF"
            data-testid="UtilitiesCommentComponent"
            backgroundColor={
                isEditable
                    ? 'neutralBackgroundMild'
                    : 'neutralBackgroundSurfaceInitial'
            }
        >
            <Stack
                direction="column"
                data-testid="UtilitiesNotesCommentComponent"
                width="100%"
            >
                <Stack justify="between" align="center" pb="1x">
                    <AvatarIdentity
                        primaryLabel={identityName}
                        fallbackText={getInitials(identityName)}
                        imgSrc={imgSrc}
                        size="xs"
                    />
                    <Box gap="3x">
                        {!isEditable &&
                            (!isReadOnly ||
                                (enableReadStatusTracking &&
                                    onUpdateReadStatus)) && (
                                <>
                                    {/* For read-only messages that are unread, use icon-only button for "Mark as read" */}
                                    {isReadOnly &&
                                    enableReadStatusTracking &&
                                    onUpdateReadStatus &&
                                    !hasBeenRead ? (
                                        <Tooltip
                                            isInteractive
                                            text={t`Mark as read`}
                                            data-id={`${id}-mark-read-tooltip`}
                                        >
                                            <Button
                                                isIconOnly
                                                level="tertiary"
                                                colorScheme="primary"
                                                size="sm"
                                                data-testid={`${id}-mark-read-button`}
                                                data-id={`${id}-mark-read-button`}
                                                startIconName="CheckCircle"
                                                label={t`Mark as read`}
                                                onClick={() => {
                                                    onUpdateReadStatus(
                                                        id,
                                                        true,
                                                    );
                                                }}
                                            />
                                        </Tooltip>
                                    ) : (
                                        <SchemaDropdown
                                            isIconOnly
                                            startIconName="HorizontalMenu"
                                            label={t`Actions`}
                                            level="tertiary"
                                            colorScheme="primary"
                                            size="sm"
                                            data-testid={`${id}-actions-dropdown`}
                                            data-id={`${id}-actions-dropdown`}
                                            items={dropdownItems}
                                        />
                                    )}
                                </>
                            )}
                    </Box>
                </Stack>
                <Box pb="3x">
                    <Stack direction="row" align="center" gap="1x">
                        <Text colorScheme="faded" size="100" type="body">
                            <DateTime
                                date={createdAt}
                                format="table_time"
                                textProps={{
                                    type: 'body',
                                    size: '100',
                                    colorScheme: 'faded',
                                }}
                            />
                        </Text>
                        {/* Show unread indicator for messages when user can't edit (is tenant) */}
                        {enableReadStatusTracking &&
                            !hasBeenRead &&
                            !canEdit && <StatusDot intent="negative" />}
                    </Stack>
                </Box>
                {!isReadOnly && isEditable ? (
                    <NoteCommentForm
                        editMode
                        maxNoteCharacters={maxNoteCharacters}
                        hasSource={hasSecondaryInput}
                        comment={value}
                        attachments={attachments}
                        source=""
                        commentLabel={commentFieldLabel}
                        sourceLabel={sourceFieldLabel}
                        labels={{
                            confirmationButton: finalConfirmationButtonLabel,
                        }}
                        attachmentConfig={{
                            showAddAttachment:
                                attachmentConfig.shouldShowAddAttachment,
                            acceptedFormats: attachmentConfig.acceptedFormats,
                            useSimpleAttachments:
                                attachmentConfig.useSimpleAttachments,
                            includeAttachmentTitle:
                                attachmentConfig.includeAttachmentTitle,
                            includeAttachmentCreationDate:
                                attachmentConfig.includeAttachmentCreationDate,
                        }}
                        onCancel={(getCurrentValues) => {
                            const currentValues = getCurrentValues?.() ?? {
                                comment: '',
                                source: '',
                            };
                            const currentValue = currentValues.comment;

                            // Check if text has changed
                            if (currentValue === value) {
                                setIsEditable(false);

                                return;
                            }

                            const cancelTitle = t`Leave without saving?`;
                            const cancelBody = finalCancelConfirmationBody;

                            openConfirmationModal({
                                title: cancelTitle,
                                body: cancelBody,
                                confirmText: t`Yes, leave without saving`,
                                cancelText: t`Cancel`,
                                type: 'primary',
                                onConfirm: () => {
                                    setIsEditable(false);
                                    closeConfirmationModal();
                                },
                                onCancel: () => {
                                    closeConfirmationModal();
                                },
                            });
                        }}
                        onSubmit={(values) => {
                            const fileMetadata = values.files.map(
                                (fileItem) => ({
                                    name:
                                        fileItem.name ||
                                        fileItem.file.file.name,
                                    originalFile: fileItem.file.file.name,
                                    creationDate:
                                        fileItem.creationDate ||
                                        new Date().toISOString(),
                                }),
                            );

                            const updatedNote: NoteUpdateDto = {
                                comment: values.comment,
                                fileMetadata,
                                'files[]': values.files.map((f) => f.file.file),
                                filesToDelete: values.deletedAttachmentIds,
                            };

                            onSave(updatedNote);
                            setIsEditable(false);
                        }}
                    />
                ) : (
                    <>
                        <Text>{value}</Text>
                        {hasSecondaryInput && (
                            <Stack direction="row" gap="1x" pt="3x">
                                {secondaryInputLabel && secondaryInputValue && (
                                    <Text type="title">
                                        {secondaryInputLabel}
                                    </Text>
                                )}
                                <Text>{secondaryInputValue}</Text>
                            </Stack>
                        )}
                    </>
                )}
                {!isEmpty(attachments) && !isEditable && (
                    <Box pt="3x">
                        <StackedList aria-label={t`Note attachments`}>
                            {attachments.map((attachment) => (
                                <StackedListItem
                                    key={`${id}-attachment-${attachment.id}`}
                                    data-id={`${id}-attachment-${attachment.id}`}
                                    primaryColumn={
                                        <Text
                                            size="200"
                                            type="body"
                                            colorScheme="neutral"
                                        >
                                            {attachment.name}
                                        </Text>
                                    }
                                    secondaryColumn={
                                        <DateTime
                                            date={attachment.createdAt}
                                            format="table_time"
                                            textProps={{
                                                type: 'body',
                                                size: '100',
                                                colorScheme: 'faded',
                                            }}
                                        />
                                    }
                                    action={
                                        <Button
                                            isIconOnly
                                            startIconName="Download"
                                            label={t`Download attachment`}
                                            type="button"
                                            level="tertiary"
                                            colorScheme="neutral"
                                            size="sm"
                                            data-id="9h-PCR1Y"
                                            onClick={() => {
                                                onDownloadAttachment?.(
                                                    attachment.id,
                                                    id,
                                                );
                                            }}
                                        />
                                    }
                                />
                            ))}
                        </StackedList>
                    </Box>
                )}

                {hasDivider && (
                    <Box pt="4x">
                        <Divider size="md" />
                    </Box>
                )}
            </Stack>
        </Box>
    );
};
