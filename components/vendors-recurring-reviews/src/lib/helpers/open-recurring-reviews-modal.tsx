import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsDetailsController,
    sharedVendorsSchedulesQuestionnairesController,
    sharedVendorsTypeformQuestionnairesController,
} from '@controllers/vendors';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import { VendorsRecurringReviewsComponent } from '../vendors-recurring-reviews-component';

export const openRecurringReviewsModal = action((vendorId?: number): void => {
    if (vendorId) {
        if (sharedVendorsDetailsController.vendorDetailsQuery.query === null) {
            sharedVendorsDetailsController.loadVendorDetails(vendorId);
        } else {
            sharedVendorsDetailsController.vendorDetailsQuery.invalidate();
        }
    }

    sharedVendorsTypeformQuestionnairesController.loadQuestionnaires({
        pagination: {
            page: 1,
            pageSize: DEFAULT_PAGE_SIZE,
        },
        globalFilter: { search: '', filters: {} },
    } as FetchDataResponseParams);

    sharedVendorsSchedulesQuestionnairesController.loadSchedulesVendorQuestionnaires(
        {
            vendorIds: vendorId ? [vendorId] : [],
        },
    );

    modalController.openModal({
        id: 'recurring-reviews-modal',
        size: 'lg',
        content: () => (
            <VendorsRecurringReviewsComponent data-id="vendors-recurring-reviews-component" />
        ),
        centered: true,
        disableClickOutsideToClose: true,
    });
});

export const finishRecurringReviewsUpdate = action((vendorId: number): void => {
    if (sharedVendorsDetailsController.vendorDetailsQuery.query !== null) {
        sharedVendorsDetailsController.vendorDetailsQuery.invalidate();
    }

    modalController.closeModal('recurring-reviews-modal');
    snackbarController.addSnackbar({
        id: `recurring-reviews-updated-${vendorId}`,
        props: {
            title: 'Recurring reviews updated successfully',
            severity: 'success',
            closeButtonAriaLabel: 'Close',
        },
    });
});
