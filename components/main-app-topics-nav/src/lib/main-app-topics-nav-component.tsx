import { isEmpty } from 'lodash-es';
import { routeController } from '@controllers/route';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { borderWidth1, neutralBorderFaded } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { NavAppLinkUi } from '@ui/nav-app-link';

export const MainAppTopicsNavComponent = observer((): React.JSX.Element => {
    const { topicsNav, isSubNavMinimized } = routeController;

    const { title = '', domainsOrder = [], domains = {} } = topicsNav ?? {};

    if (isEmpty(domainsOrder)) {
        return <>{null}</>;
    }

    const padding = isSubNavMinimized
        ? ({ py: 'lg' } as const as Record<string, string>)
        : ({ p: 'lg' } as const as Record<string, string>);

    return (
        <Stack
            direction="column"
            {...padding}
            gap="lg"
            data-id="main-app-topics-nav-component-stack"
            width={isSubNavMinimized ? '50px' : '200px'}
            minWidth={isSubNavMinimized ? '50px' : '200px'}
            height="100%"
            minHeight="0"
            overflow="auto"
            /**
             * Box supports border properties and can render as a span to avoid
             * disrupting the flex layout. We can use Box with as="span" instead
             * of inline styles for a more consistent approach but Box still doesn't support border sides.
             */
            style={{
                borderRight: `${borderWidth1} solid ${neutralBorderFaded}`,
            }}
        >
            <Stack
                py="sm"
                px="lg"
                gap="lg"
                direction="row"
                align="center"
                justify="between"
                data-id="main-app-topics-nav-component-title-stack"
            >
                {title && !isSubNavMinimized && (
                    <Box py="xs">
                        <Text type="title" size="300" colorScheme="neutral">
                            {title}
                        </Text>
                    </Box>
                )}
                <Button
                    isIconOnly
                    size="sm"
                    label={t`Toggle page navigation`}
                    level="tertiary"
                    colorScheme="neutral"
                    startIconName={
                        isSubNavMinimized
                            ? 'ShowSidebarMenu'
                            : 'HideSidebarMenu'
                    }
                    onClick={() => {
                        routeController.setMinimizeSubNav(!isSubNavMinimized);
                    }}
                />
            </Stack>
            {!isSubNavMinimized && (
                <Stack
                    gap="lg"
                    direction="column"
                    data-id="main-app-topics-nav-component-title-stack"
                >
                    {domainsOrder.map((domainId) => {
                        const {
                            label: domainLabel,
                            hideLabel,
                            topicsOrder,
                            topics,
                        } = domains[domainId];

                        return (
                            <Stack
                                key={domainLabel}
                                direction="column"
                                data-id="sBkbOkMK"
                            >
                                {!hideLabel && (
                                    <Box px="lg" py="sm">
                                        <Text
                                            allowBold
                                            size="100"
                                            type="title"
                                            colorScheme="neutral"
                                        >
                                            {/* TODO: Remove strong tag when tokens are reworked - this is not an appropriate use of strong, DO NOT COPY */}
                                            <strong>{domainLabel}</strong>
                                        </Text>
                                    </Box>
                                )}

                                <Box pt="xs" pb="xs">
                                    <nav aria-label={domainLabel}>
                                        {topicsOrder.map((topicId) => {
                                            const {
                                                label: topicLabel,
                                                topicPath,
                                            } = topics[topicId];

                                            return (
                                                <NavAppLinkUi
                                                    key={topicId}
                                                    label={topicLabel}
                                                    href={topicPath}
                                                    type="expanded"
                                                    data-id="zeUHvsh_"
                                                />
                                            );
                                        })}
                                    </nav>
                                </Box>
                            </Stack>
                        );
                    })}
                </Stack>
            )}
        </Stack>
    );
});
