import { useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedActiveAccessReviewPeriodsController } from '@controllers/access-reviews';
import { Button } from '@cosmos/components/button';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { activeDatatableModel } from '@models/access-review';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { accessReviewApplicationToDatatableAdaptor } from './adaptors/access-review-data-table.adaptor';
import { handleAccessReviewRowNavigation } from './helpers/active-data-table.helper';
import { useBuildAccessReviewApplicationActions } from './hooks/use-build-access-review-application-actions.hook';

const MINIMUM_TOTAL_APPLICATIONS = 1;

interface AccessReviewActiveDataTableProps {
    hasActivePeriod?: boolean;
    onCreateReviewPeriod: () => void;
    onEditReviewPeriod: () => void;
}

export const AccessReviewActiveDataTable = observer(
    ({
        hasActivePeriod,
        onCreateReviewPeriod,
        onEditReviewPeriod,
    }: AccessReviewActiveDataTableProps): React.JSX.Element => {
        const navigate = useNavigate();

        const {
            isLoading,
            applicationsWithProviderInfo,
            totalApplications,
            loadActiveAccessReviewPeriods,
        } = sharedActiveAccessReviewPeriodsController;
        const { currentWorkspaceId: workspaceId } = sharedWorkspacesController;
        const { hasLimitedAccess } = sharedFeatureAccessModel;
        const canCreatePeriod = !hasLimitedAccess;
        const { columns } = activeDatatableModel;

        const buildAccessReviewTableActions =
            useBuildAccessReviewApplicationActions();

        const data = useMemo(
            () =>
                accessReviewApplicationToDatatableAdaptor(
                    applicationsWithProviderInfo,
                    buildAccessReviewTableActions,
                ),
            [applicationsWithProviderInfo, buildAccessReviewTableActions],
        );

        const emptyStateProps: EmptyStateProps = useMemo(() => {
            const canManageApplications = !hasLimitedAccess;

            if (!hasActivePeriod) {
                return {
                    imageSize: 'md',
                    title: t`Use review periods to carry out time bound access reviews`,
                    description: t`Initiate periodic reviews and upon completion, evidence generated is automatically linked to relevant controls, ensuring compliance and saving you time.`,
                    illustrationName: 'AddCircle',
                    leftAction: canCreatePeriod ? (
                        <Button
                            colorScheme="primary"
                            size="md"
                            label={t`Create review period`}
                            onClick={onCreateReviewPeriod}
                        />
                    ) : null,
                    rightAction: (
                        <AppLink
                            isExternal
                            size="md"
                            href="https://help.drata.com/en/articles/8895897-access-reviews#h_4d04aa7427"
                        >
                            {t`Learn more about access review periods`}
                        </AppLink>
                    ),
                } as EmptyStateProps;
            }

            if (totalApplications < MINIMUM_TOTAL_APPLICATIONS) {
                return {
                    title: t`No applications in the review period`,
                    description: t`Initiate periodic reviews and upon completion, evidence generated is automatically linked to relevant controls, ensuring compliance and saving you time.`,
                    illustrationName: 'AddCircle',
                    leftAction: canCreatePeriod ? (
                        <Button
                            colorScheme="primary"
                            size="md"
                            label={t`Edit review period`}
                            onClick={onEditReviewPeriod}
                        />
                    ) : null,
                    rightAction: canManageApplications ? (
                        <AppLink
                            label={t`Go to connections`}
                            href={`/workspaces/${workspaceId}/connections/all/active`}
                        />
                    ) : null,
                } as EmptyStateProps;
            }

            return {
                title: t`No applications found`,
                description: t`No applications found`,
            };
        }, [
            hasActivePeriod,
            canCreatePeriod,
            onCreateReviewPeriod,
            totalApplications,
            hasLimitedAccess,
            onEditReviewPeriod,
            workspaceId,
        ]);

        return (
            <AppDatatable
                isFullPageTable
                isLoading={isLoading}
                tableId="datatable-governance-access-review-active"
                columns={columns}
                total={totalApplications}
                data-testid="AccessReviewActiveDataTable"
                data-id="u1Q5xT_N"
                data={data}
                emptyStateProps={emptyStateProps}
                tableSearchProps={{
                    hideSearch: true,
                }}
                defaultPaginationOptions={{
                    pageIndex: 0,
                    pageSize: DEFAULT_PAGE_SIZE,
                    pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                }}
                onFetchData={loadActiveAccessReviewPeriods}
                onRowClick={({ row }) => {
                    handleAccessReviewRowNavigation(
                        row.id,
                        row.reviewPeriodId,
                        row.status,
                        workspaceId,
                        navigate,
                    );
                }}
            />
        );
    },
);
