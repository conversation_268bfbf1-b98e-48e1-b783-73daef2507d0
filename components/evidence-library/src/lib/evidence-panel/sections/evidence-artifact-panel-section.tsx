import {
    openRenewalDateModal,
    sharedDownloadEvidenceArtifactModel,
} from '@components/evidence-library';
import { sharedControlsExternalEvidenceController } from '@controllers/controls';
import {
    sharedEvidenceDetailsController,
    sharedEvidenceDetailsFileDownloadController,
} from '@controllers/evidence-library';
import { Accordion } from '@cosmos/components/accordion';
import { ActionStack } from '@cosmos/components/action-stack';
import { Feedback } from '@cosmos/components/feedback';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import {
    type EvidenceStatusType,
    getEvidenceStatusDescriptor,
} from '@helpers/evidence';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';
import { sharedEvidencePanelModel } from '../model/evidence-panel.model';

export const EvidenceArtifactPanelSection = observer((): React.JSX.Element => {
    const { isLoading, evidenceDetailsData } = sharedEvidenceDetailsController;
    const { downloadFileAsBlobAttachment } =
        sharedEvidenceDetailsFileDownloadController;
    const { loadFileUrl: downloadEvidenceFile } =
        sharedDownloadEvidenceArtifactModel;
    const {
        buildArtifactActions,
        sourceTable,
        currentEvidenceUnionPanelDetails,
    } = sharedEvidencePanelModel;
    const { currentLibraryVersion } = sharedEvidenceDetailsModel;
    const { controlExternalEvidence } =
        sharedControlsExternalEvidenceController;
    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    const title = (() => {
        if (sourceTable === 'EXTERNAL_EVIDENCE') {
            if (controlExternalEvidence?.url) {
                return controlExternalEvidence.url;
            }

            if (controlExternalEvidence?.file) {
                return (
                    controlExternalEvidence.file.split('/').pop() ||
                    controlExternalEvidence.name ||
                    t`NONE`
                );
            }
        }

        return currentLibraryVersion?.type === 'URL'
            ? currentLibraryVersion.source
            : currentLibraryVersion?.metadata.originalFileName;
    })();

    const evidenceStatusDescriptor = getEvidenceStatusDescriptor(
        currentEvidenceUnionPanelDetails?.status as EvidenceStatusType,
        0,
    );

    const isLibraryVersionNone =
        sourceTable === 'EXTERNAL_EVIDENCE'
            ? false
            : currentLibraryVersion?.type === 'NONE';

    return (
        <Stack direction="column" gap="2xl" data-id="pMJ_viIL">
            <Text type="title" size="400">
                <Trans>Artifact</Trans>
            </Text>

            {isLoading ? (
                <Skeleton barCount={1} />
            ) : (
                <Accordion
                    key={currentLibraryVersion?.id}
                    title={title ?? t`NONE`}
                    titleType="md"
                    data-id={`evidence-details-control-${currentLibraryVersion?.id}`}
                    supportingContent={
                        evidenceStatusDescriptor && (
                            <Feedback
                                title={evidenceStatusDescriptor.content}
                                severity={evidenceStatusDescriptor.severity}
                            />
                        )
                    }
                    body={
                        <Stack direction="column" gap="xl">
                            <Stack gap="xl" align="end">
                                <KeyValuePair
                                    label={t`Creation date`}
                                    type="REACT_NODE"
                                    value={(() => {
                                        const creationDate =
                                            sourceTable === 'EXTERNAL_EVIDENCE'
                                                ? controlExternalEvidence?.createdAt
                                                : currentLibraryVersion?.filedAt;

                                        return creationDate ? (
                                            <Text>
                                                {formatDate(
                                                    'sentence',
                                                    creationDate,
                                                )}
                                            </Text>
                                        ) : (
                                            <EmptyValue label="—" />
                                        );
                                    })()}
                                />
                                <KeyValuePair
                                    label={t`Renewal date`}
                                    type="REACT_NODE"
                                    value={(() => {
                                        const renewalDate =
                                            sourceTable === 'EXTERNAL_EVIDENCE'
                                                ? controlExternalEvidence?.renewalDate
                                                : currentLibraryVersion?.renewalDate;

                                        return renewalDate ? (
                                            <Text>
                                                {formatDate(
                                                    'sentence',
                                                    renewalDate,
                                                )}
                                            </Text>
                                        ) : (
                                            <EmptyValue label="—" />
                                        );
                                    })()}
                                />
                            </Stack>
                            {!isLibraryVersionNone &&
                                sourceTable === 'LIBRARY_DOCUMENT' &&
                                hasWriteControlPermission && (
                                    <ActionStack
                                        gap="lg"
                                        actions={buildArtifactActions(
                                            {
                                                openRenewalDateModal: action(
                                                    () => {
                                                        openRenewalDateModal(
                                                            evidenceDetailsData?.id,
                                                        );
                                                    },
                                                ),
                                                downloadFile: action(() => {
                                                    if (
                                                        !currentLibraryVersion
                                                    ) {
                                                        return;
                                                    }

                                                    downloadFileAsBlobAttachment(
                                                        currentLibraryVersion.id,
                                                    );
                                                }),
                                                viewFile: action(() => {
                                                    if (
                                                        !currentLibraryVersion
                                                    ) {
                                                        return;
                                                    }
                                                    downloadEvidenceFile(
                                                        currentLibraryVersion,
                                                    );
                                                }),
                                                openUrl: () => {
                                                    window.open(
                                                        currentLibraryVersion?.source,
                                                    );
                                                },
                                            },
                                            currentLibraryVersion,
                                        )}
                                    />
                                )}
                        </Stack>
                    }
                />
            )}
        </Stack>
    );
});
