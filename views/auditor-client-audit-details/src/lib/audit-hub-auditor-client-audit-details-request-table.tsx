import { useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { modalController } from '@controllers/modal';
import type { DatatableRef } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { AuditHubAuditorClientAuditAddBulkRequestModal } from './audit-hub-auditor-client-audit-add-bulk-request-modal';
import {
    FILTER_VIEW_MODE_PROPS,
    getAuditHubAuditorsListTableColumns,
    getEmptyStateProps,
    getTableSearchProps,
} from './audit-hub-audits-details-page-constants';
import { openAddSingleRequestModal } from './helpers/open-add-single-request-modal.helper';
import { CustomerRequestBulkActionsModel } from './models/customer-request-bulk-actions.model';

export const AuditHubAuditDetailsRequestTable = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const {
            customerRequests,
            isLoading,
            total,
            getCustomerRequestList,
            filterProps,
            currentAppliedFilters,
        } = sharedCustomerRequestsController;
        const { clientId } = sharedCustomerRequestDetailsController;

        const { auditSummaryByIdData } = sharedAuditorController;

        const datatableRef = useRef<DatatableRef>(null);

        const { bulkActions, handleRowSelection } =
            new CustomerRequestBulkActionsModel(
                currentAppliedFilters,
                datatableRef,
            );

        const navigateToRequestDetail = (requestId: number) => {
            navigate(
                `/audit-hub/clients/${clientId}/audits/${auditSummaryByIdData?.auditorFrameworkId}/evidence-requests/${requestId}/overview`,
            );
        };

        return (
            <AppDatatable
                isRowSelectionEnabled
                getRowId={(row) => String(row.id)} // Required for selection
                imperativeHandleRef={datatableRef}
                isLoading={isLoading}
                tableId="audit-hub-datatable-audit-details-requests"
                total={total}
                data={customerRequests}
                columns={getAuditHubAuditorsListTableColumns()}
                filterProps={filterProps}
                data-testid="AuditHubAuditDetailsRequestTable"
                data-id=""
                tableSearchProps={getTableSearchProps()}
                filterViewModeProps={FILTER_VIEW_MODE_PROPS}
                emptyStateProps={getEmptyStateProps()}
                bulkActionDropdownItems={bulkActions}
                tableActions={[
                    {
                        actionType: 'dropdown',
                        id: 'add-request-dropdown',
                        typeProps: {
                            label: t`Add Request`,
                            level: 'secondary',
                            endIconName: 'ChevronDown',
                            align: 'end',
                            items: [
                                {
                                    id: 'add-single-request',
                                    label: t`Add single request`,
                                    type: 'item',
                                    value: 'ADD_SINGLE',
                                    onClick: openAddSingleRequestModal,
                                },
                                {
                                    id: 'add-bulk-requests',
                                    label: t`Add requests in bulk`,
                                    type: 'item',
                                    value: 'ADD_BULK',
                                    onClick: () => {
                                        modalController.openModal({
                                            id: 'add-bulk-requests-modal',
                                            content: () => (
                                                <AuditHubAuditorClientAuditAddBulkRequestModal data-id="add-bulk-requests-modal" />
                                            ),
                                            centered: true,
                                            disableClickOutsideToClose: true,
                                            size: 'md',
                                        });
                                    },
                                },
                            ],
                        },
                    },
                ]}
                onRowSelection={handleRowSelection}
                onFetchData={getCustomerRequestList}
                onRowClick={({ row }) => {
                    navigateToRequestDetail(row.id);
                }}
            />
        );
    },
);
