import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import {
    type CustomerRequestsListQuery,
    sharedCustomerRequestsController,
} from '@controllers/customer-requests';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export class CustomerRequestBulkActionsModel {
    selectedCustomerRequestIds: number[] = [];
    isAllRowsSelected = false;
    datatableRef: React.RefObject<DatatableRef>;
    appliedFilters: CustomerRequestsListQuery | null;

    constructor(datatableRef: React.RefObject<DatatableRef>) {
        makeAutoObservable(this);
        this.appliedFilters =
            sharedCustomerRequestsController.currentAppliedFilters;
        this.datatableRef = datatableRef;
    }

    get bulkActions(): BulkAction[] {
        return [
            {
                id: 'bulk-change-to-new',
                actionType: 'button',
                typeProps: {
                    label: t`Change to New`,
                    level: 'tertiary',
                    onClick: (): void => {
                        this.updateCustomerRequestStatus('OUTSTANDING');
                    },
                },
            },
            {
                id: 'bulk-change-to-prepared',
                actionType: 'button',
                typeProps: {
                    label: t`Change to Prepared`,
                    level: 'tertiary',
                    onClick: (): void => {
                        this.updateCustomerRequestStatus('IN_REVIEW');
                    },
                },
            },
            {
                id: 'bulk-change-to-completed',
                actionType: 'button',
                typeProps: {
                    label: t`Change to Completed`,
                    level: 'tertiary',
                    onClick: (): void => {
                        this.updateCustomerRequestStatus('ACCEPTED');
                    },
                },
            },
            {
                id: 'bulk-delete-requests',
                actionType: 'button',
                typeProps: {
                    label: t`Delete Requests`,
                    level: 'tertiary',
                    onClick: (): void => {
                        openConfirmationModal({
                            title: t`Delete Confirmation`,
                            body: t`Are you sure?`,
                            confirmText: t`Ok`,
                            cancelText: t`Cancel`,
                            type: 'danger',
                            size: 'md',
                            onConfirm: () => {
                                sharedCustomerRequestDetailsController.deleteCustomerRequests(
                                    {
                                        ...this.appliedFilters,
                                        requestIdList:
                                            this.selectedCustomerRequestIds,
                                        statusFilter:
                                            this.appliedFilters?.status,
                                        selectAll: this.isAllRowsSelected,
                                        framework:
                                            sharedCustomerRequestsController.frameworkId,
                                    },
                                );

                                when(
                                    () =>
                                        !sharedCustomerRequestDetailsController
                                            .deleteCustomerRequestMutation
                                            .isPending,
                                    () => {
                                        if (
                                            !sharedCustomerRequestDetailsController
                                                .deleteCustomerRequestMutation
                                                .hasError
                                        ) {
                                            this.onSuccess();
                                        }
                                        closeConfirmationModal();
                                    },
                                );
                            },
                            onCancel: closeConfirmationModal,
                        });
                    },
                },
            },
        ];
    }

    updateCustomerRequestStatus(
        newStatus: 'OUTSTANDING' | 'IN_REVIEW' | 'ACCEPTED',
    ): void {
        openConfirmationModal({
            title: t`Update Confirmation`,
            body: t`Are you sure?`,
            confirmText: t`Ok`,
            cancelText: t`Cancel`,
            type: 'primary',
            size: 'md',
            onConfirm: () => {
                sharedCustomerRequestDetailsController.updateCustomerRequestStatus(
                    {
                        ...this.appliedFilters,
                        requestIds: this.selectedCustomerRequestIds,
                        status: newStatus,
                        statusFilter: this.appliedFilters?.status,
                        selectAll: this.isAllRowsSelected,
                        framework: sharedCustomerRequestsController.frameworkId,
                    },
                );

                when(
                    () =>
                        !sharedCustomerRequestDetailsController
                            .updateStatusMutation.isPending,
                    () => {
                        if (
                            !sharedCustomerRequestDetailsController
                                .updateStatusMutation.hasError
                        ) {
                            this.onSuccess();
                        }
                        closeConfirmationModal();
                    },
                );
            },
            onCancel: closeConfirmationModal,
        });
    }

    onSuccess = (): void => {
        this.datatableRef.current?.resetRowSelection();
    };

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const selectedIds = Object.keys(selectedRows);

        this.selectedCustomerRequestIds = selectedIds.map(Number);
        this.isAllRowsSelected = isAllRowsSelected;
    };
}
