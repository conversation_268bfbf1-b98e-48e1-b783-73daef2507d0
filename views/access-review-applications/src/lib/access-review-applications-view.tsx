import { useEffect, useMemo } from 'react';
import { AppDatatable } from '@components/app-datatable';
import {
    sharedAccessReviewController,
    sharedActiveAccessReviewPeriodsController,
} from '@controllers/access-reviews';
import { Button } from '@cosmos/components/button';
import {
    type DatatableProps,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import type { AccessApplicationSummaryResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { getAccessReviewApplicationColumns } from './constants/access-review-applications.constants';
import { openAccessReviewAddApplicationModal } from './helpers/access-review-open-add-application-modal.helper';
import type { ApplicationData } from './types/access-review-applications.types';

/**
 * This function is used to adapt the data from the API to the data that the datatable expects.
 * It is necessary because the API returns some data as nullable, but the datatable expects all data to be non-nullable.
 */
const accessReviewAdaptor = (
    dtoData: AccessApplicationSummaryResponseDto[] | null,
): ApplicationData[] => {
    return (dtoData ?? []).map((application) => ({
        ...application,
        hasFailed: application.hasFailed ?? false,
        logo: application.logo ?? '', // Provide empty string as default for nullable logo
    }));
};

export const AccessReviewApplicationsView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const {
        isLoading: isLoadingAccessReview,
        accessReviewList,
        accessReviewListTotal,
        loadAccessReview,
    } = sharedAccessReviewController;

    const { hasLimitedAccess, isAccessReviewReadEnabled } =
        sharedFeatureAccessModel;

    const {
        canCreateReviewPeriod,
        isLoading: isLoadingActiveAccessReviewPeriods,
    } = sharedActiveAccessReviewPeriodsController;

    const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

    const adaptedData = accessReviewAdaptor(accessReviewList);

    const columns = useMemo(() => {
        const allColumns = getAccessReviewApplicationColumns();

        return isAccessReviewReadEnabled ? allColumns : allColumns.slice(1);
    }, [isAccessReviewReadEnabled]);

    useEffect(() => {
        sharedAccessReviewController.resetPagination();
    }, [isAccessReviewReadEnabled]);

    const tableActions: DatatableProps<ApplicationData>['tableActions'] =
        !hasLimitedAccess && isAccessReviewReadEnabled
            ? [
                  {
                      actionType: 'button',
                      id: 'add-application-action-button',
                      typeProps: {
                          label: t`Add application`,
                          level: 'secondary',
                          onClick: () => {
                              openAccessReviewAddApplicationModal();
                          },
                      },
                  },
                  ...(canCreateReviewPeriod &&
                  !isLoadingActiveAccessReviewPeriods
                      ? [
                            {
                                actionType: 'button' as const,
                                id: 'create-review-period-action-button',
                                typeProps: {
                                    label: t`Create review period`,
                                    onClick: () => {
                                        navigate(
                                            `/workspaces/${workspaceId}/governance/access-review/create-period`,
                                        );
                                    },
                                },
                            },
                        ]
                      : []),
              ]
            : [];

    return (
        <AppDatatable
            isFullPageTable
            isLoading={isLoadingAccessReview}
            tableId="datatable-uar-applications"
            data-id="datatable-uar-applications"
            data={adaptedData}
            columns={columns}
            total={accessReviewListTotal}
            tableActions={tableActions}
            defaultPaginationOptions={{
                pageIndex: 0,
                pageSize: DEFAULT_PAGE_SIZE,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
            }}
            emptyStateProps={{
                title: hasLimitedAccess
                    ? t`You need to connect Access Review applications to monitor your users access levels.`
                    : t`Access review applications`,
                illustrationName: 'Access',
                description: hasLimitedAccess
                    ? t`Contact your administrator to review issue`
                    : t`Go To Connections`,
                leftAction: hasLimitedAccess ? null : (
                    <Button
                        label={t`Set up connections`}
                        level="primary"
                        onClick={() => {
                            navigate(
                                `/workspaces/${workspaceId}/connections/all/active`,
                            );
                        }}
                    />
                ),
            }}
            tableSearchProps={{
                hideSearch: true,
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: t`Pin filters to page`,
                    toggleUnpinnedLabel: t`Move filters to dropdown`,
                },
                viewMode: 'toggleable',
            }}
            onFetchData={loadAccessReview}
            onRowClick={({ row }) => {
                const { id, clientType } = row;

                navigate(
                    `/workspaces/${workspaceId}/governance/access-review/applications/${id}/personnel/${clientType}`,
                );
            }}
        />
    );
});
