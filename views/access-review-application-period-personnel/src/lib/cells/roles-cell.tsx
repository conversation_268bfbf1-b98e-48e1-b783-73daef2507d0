import { isArray, isEmpty, isNil, isObject } from 'lodash-es';
import { useRef, useState } from 'react';
import { Button } from '@cosmos/components/button';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension48x } from '@cosmos/constants/tokens';
import type { UserAccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { AppLink } from '@ui/app-link';

export const RolesCell = ({
    row: { original },
}: {
    row: { original: UserAccessReviewPeriodApplicationResponseDto };
}): React.JSX.Element => {
    const { roles, clientType, externalUserProfile } = original;
    const MINIMUM_VALUE = 1;
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);

    if (
        clientType === 'OKTA_IDENTITY' &&
        !isNil(externalUserProfile) &&
        isEmpty(roles)
    ) {
        return (
            <AppLink
                isExternal
                href={externalUserProfile[0]}
                label="View account details"
                size="sm"
            />
        );
    }

    if (!isNil(roles) && isArray(roles)) {
        type Role = string | { name: string };
        const firstGroup = isObject(roles[0])
            ? (roles[0] as unknown as { name: string }).name
            : roles[0];

        return roles.length > MINIMUM_VALUE ? (
            <>
                <Text type="body" size="200" as="span">
                    {firstGroup}
                </Text>
                <Button
                    ref={buttonRef}
                    level="tertiary"
                    colorScheme="neutral"
                    size="sm"
                    label={`+${roles.length - MINIMUM_VALUE}`}
                    data-id="groups-cell-button"
                    onClick={() => {
                        setIsPopoverOpen(true);
                    }}
                />
                <Popover
                    anchor={buttonRef.current}
                    isOpen={isPopoverOpen}
                    placement="bottom-start"
                    padding="md"
                    data-id="groups-cell-popover"
                    content={
                        <Stack
                            direction="column"
                            gap="xs"
                            maxHeight={dimension48x}
                            overflowY="auto"
                            minWidth={dimension48x}
                        >
                            {(roles as unknown as Role[])
                                .slice(1)
                                .map((groupName) => (
                                    <Text
                                        type="body"
                                        size="200"
                                        data-id="mByjjbCx"
                                        key={
                                            isObject(groupName)
                                                ? groupName.name
                                                : groupName
                                        }
                                    >
                                        {isObject(groupName)
                                            ? groupName.name
                                            : groupName}
                                    </Text>
                                ))}
                        </Stack>
                    }
                    onDismiss={() => {
                        setIsPopoverOpen(false);
                    }}
                />
            </>
        ) : (
            <Text
                type="body"
                size="200"
                data-id="qW2ZQjUr"
                data-testid="GroupsCell"
                as="p"
            >
                {firstGroup}
            </Text>
        );
    }

    return <EmptyStateTableCell data-id="qW2ZQjUr" data-testid="RolesCell" />;
};
