import { z } from 'zod';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';
import { renderCategoriesComponent } from '../helpers/render-categories-component.helper';

const CODE_MAX_LENGTH = 191;
const LONG_TEXT_MAX_LENGTH = 30000;

export const buildFrameworkOverviewSchema = (): FormSchema => {
    const { requirement } = sharedRequirementDetailsController;

    return {
        code: {
            type: 'text',
            label: t`Requirement code`,
            helpText: t`This code should uniquely identify the requirement.`,
            initialValue: requirement?.name ?? '',
            validator: z.string().min(1).max(CODE_MAX_LENGTH),
        },
        name: {
            type: 'text',
            label: t`Requirement name`,
            helpText: t`This name should succinctly describe the requirement.`,
            initialValue: requirement?.description ?? '',
            validator: z.string().min(1).max(LONG_TEXT_MAX_LENGTH),
        },
        category: {
            type: 'custom',
            render: renderCategoriesComponent,
            label: t`Category`,
        },
        description: {
            type: 'textarea',
            label: t`Description`,
            helpText: t`This description should explain the requirement standards.`,
            initialValue: requirement?.longDescription ?? '',
            validator: z.string().max(LONG_TEXT_MAX_LENGTH),
        },
        additionalInfo: {
            type: 'textarea',
            label: t`Additional info`,
            initialValue: requirement?.additionalInfo ?? '',
            validator: z.string().max(LONG_TEXT_MAX_LENGTH),
        },
    };
};
