import { isNil } from 'lodash-es';
import { useMemo } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import {
    sharedCustomFrameworkUpdateRequirementController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import {
    sharedRequirementCreateController,
    sharedRequirementDetailsController,
} from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { FrameworksOverviewViewCard } from './components/frameworks-overview-card.component';
import { FrameworksOverviewFormCard } from './components/frameworks-overview-form-card.component';

export const FrameworksOverviewView = observer((): React.JSX.Element => {
    const { frameworkDetails, isLoading } = sharedFrameworkDetailsController;
    const { isPending, hasError } =
        sharedCustomFrameworkUpdateRequirementController;

    const { requirement } = sharedRequirementDetailsController;
    const { formRef, triggerSubmit } = useFormSubmit();

    const isCustomFramework = useMemo(() => {
        if (isLoading || !frameworkDetails) {
            return false;
        }

        return frameworkDetails.tag === 'CUSTOM';
    }, [frameworkDetails, isLoading]);

    const handleEdit = action(() => {
        if (isNil(requirement)) {
            return;
        }

        sharedRequirementCreateController.loadRequirementCategories(
            Number(requirement.customFrameworkId),
        );

        when(
            () =>
                !sharedRequirementCreateController.isRequirementCategoriesLoading,
            () => {
                sharedRequirementCreateController.setSelectedCategory(
                    requirement.customCategory ?? null,
                );
            },
        );
    });

    return (
        <ViewEditCardComponent
            hasMutationError={hasError}
            isMutationPending={isPending}
            title={t`Overview`}
            data-testid="FrameworksOverviewView"
            data-id="fOBpfP4c"
            readOnlyComponent={<FrameworksOverviewViewCard />}
            editComponent={
                isCustomFramework ? (
                    <FrameworksOverviewFormCard formRef={formRef} />
                ) : null
            }
            onEdit={handleEdit}
            onSave={triggerSubmit}
        />
    );
});
