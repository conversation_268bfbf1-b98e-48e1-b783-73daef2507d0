import { sharedCustomFrameworkUpdateRequirementController } from '@controllers/frameworks';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import type { UpdateCustomRequirementRequestDto } from '@globals/api-sdk/types';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { FormValues } from '@ui/forms';

export const frameworksOverviewSubmit = action((values: FormValues) => {
    const { currentWorkspace } = sharedWorkspacesController;
    const { requirement } = sharedRequirementDetailsController;
    const category = values.category as { value: string } | null;

    if (!currentWorkspace || !requirement) {
        return;
    }

    const body: UpdateCustomRequirementRequestDto = {
        id: requirement.id,
        code: String(values.code),
        name: String(values.name),
        description: String(values.description),
        additionalInfo: String(values.additionalInfo),
        category: String(category?.value ?? ''),
        workspaceId: currentWorkspace.id,
    };

    sharedCustomFrameworkUpdateRequirementController.updateCustomRequirement(
        body,
    );
});
