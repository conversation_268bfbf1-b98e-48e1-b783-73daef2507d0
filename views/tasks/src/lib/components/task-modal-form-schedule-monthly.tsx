import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import { FormField } from '@cosmos/components/form-field';
import { RadioField } from '@cosmos/components/radio-field';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { dimension36x } from '@cosmos/constants/tokens';
import { RecurringType } from '@drata/recurring-schedule';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import {
    getDayOptions,
    getTaskCardinalOptions,
    getTasksWeekdaysOptions,
    sharedTaskForm,
    type TaskScheduleWeekday,
    type TaskScheduleWeekNumber,
} from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

const MonthlyDayOfMonth = observer(() => {
    return (
        <Stack direction="row" gap="2x" data-id="uJf3dS2-" align="center">
            <Box>
                <RadioField
                    formId={TASKS_FORM_ID}
                    label={t`Specific day of the month`}
                    name="specificDayOfMonth"
                    value="specificDayOfMonth"
                    checked={
                        sharedTaskForm.currentValues.scheduleMonthlyType ===
                        'specificDayOfMonth'
                    }
                    onChange={() => {
                        sharedTaskForm.update('scheduleMonthlyType')(
                            'specificDayOfMonth',
                        );
                    }}
                />
            </Box>
            <Box width={dimension36x}>
                <ComboboxField
                    shouldHideLabel
                    formId={TASKS_FORM_ID}
                    name="scheduleMonthlySpecificDay"
                    label={t`Day of month`}
                    options={getDayOptions()}
                    loaderLabel={t`Loading days...`}
                    key={sharedTaskForm.currentValues.scheduleDayOfMonth?.id}
                    defaultValue={toJS(
                        sharedTaskForm.currentValues.scheduleDayOfMonth,
                    )}
                    onChange={sharedTaskForm.update('scheduleDayOfMonth')}
                    onFocus={() => {
                        sharedTaskForm.update('scheduleMonthlyType')(
                            'specificDayOfMonth',
                        );
                    }}
                />
            </Box>
        </Stack>
    );
});

const MonthlyWeekday = observer(() => {
    return (
        <Stack direction="row" gap="2x" data-id="uJf3dS2-" align="center">
            <Box>
                <RadioField
                    formId={TASKS_FORM_ID}
                    label={t`Specific day of the week`}
                    name="specificDayOfWeek"
                    value="specificDayOfWeek"
                    checked={
                        sharedTaskForm.currentValues.scheduleMonthlyType ===
                        'specificDayOfWeek'
                    }
                    onChange={() => {
                        sharedTaskForm.update('scheduleMonthlyType')(
                            'specificDayOfWeek',
                        );
                    }}
                />
            </Box>
            <Box width={dimension36x}>
                <SelectField
                    shouldHideLabel
                    formId={TASKS_FORM_ID}
                    name="scheduleWeekNumber"
                    label={t`Week number`}
                    options={getTaskCardinalOptions()}
                    key={sharedTaskForm.currentValues.scheduleWeekNumber?.id}
                    value={toJS(
                        sharedTaskForm.currentValues.scheduleWeekNumber,
                    )}
                    onChange={(val) => {
                        sharedTaskForm.update('scheduleWeekNumber')(
                            val as TaskScheduleWeekNumber,
                        );
                    }}
                />
            </Box>
            <Box width={dimension36x}>
                <SelectField
                    shouldHideLabel
                    formId={TASKS_FORM_ID}
                    name="scheduleWeekday"
                    label={t`Weekday`}
                    options={getTasksWeekdaysOptions()}
                    value={toJS(sharedTaskForm.currentValues.scheduleWeekday)}
                    key={sharedTaskForm.currentValues.scheduleWeekday?.id}
                    onChange={(val) => {
                        sharedTaskForm.update('scheduleWeekday')(
                            val as TaskScheduleWeekday,
                        );
                    }}
                    onFocus={() => {
                        sharedTaskForm.update('scheduleMonthlyType')(
                            'specificDayOfWeek',
                        );
                    }}
                />
            </Box>
        </Stack>
    );
});

const MonthlyOptions = observer(() => {
    return (
        <Stack direction="column" gap="2x" data-id="uJf3dS2-">
            <MonthlyDayOfMonth />
            <MonthlyWeekday />
        </Stack>
    );
});

export const TaskModalFormScheduleMonthly = observer(() => {
    if (
        sharedTaskForm.currentValues.scheduleType?.value !==
        RecurringType.MONTHLY
    ) {
        return null;
    }

    const error = '';

    return (
        <FormField
            formId={TASKS_FORM_ID}
            label={t`Repeat on`}
            name="scheduleMonthlyOptions"
            renderInput={() => <MonthlyOptions data-id="keXSsZQ4" />}
            data-id="smVuf8Wn"
            feedback={{
                type: 'error',
                message: error,
            }}
        />
    );
});
