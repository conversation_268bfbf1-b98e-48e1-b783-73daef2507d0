import { Box } from '@cosmos/components/box';
import { CheckboxFieldGroup } from '@cosmos/components/checkbox-field-group';
import { RecurringType, type Weekday } from '@drata/recurring-schedule';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { getTaskShortWeekdaysOptions, sharedTaskForm } from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

export const TaskModalFormScheduleWeekly = observer(() => {
    const options = getTaskShortWeekdaysOptions();

    if (
        sharedTaskForm.currentValues.scheduleType?.value !==
        RecurringType.WEEKLY
    ) {
        return null;
    }

    return (
        <Box maxWidth="fit-content" data-id="Irvymvvh">
            <CheckboxFieldGroup
                formId={TASKS_FORM_ID}
                label={t`Repeat on`}
                name="scheduleDaysOfWeek"
                value={toJS(sharedTaskForm.currentValues.scheduleDaysOfWeek)}
                options={options}
                cosmosUseWithCaution_forceOptionOrientation="horizontal"
                data-id="ig7vbqNC"
                onChange={(val) => {
                    sharedTaskForm.update('scheduleDaysOfWeek')(
                        val as Weekday[],
                    );
                }}
            />
        </Box>
    );
});
