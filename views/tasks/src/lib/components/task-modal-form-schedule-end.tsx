import { Box } from '@cosmos/components/box';
import {
    DatePickerField,
    type TDateISODate,
} from '@cosmos/components/date-picker-field';
import { FormField } from '@cosmos/components/form-field';
import { RadioField } from '@cosmos/components/radio-field';
import { Stack } from '@cosmos/components/stack';
import { dimension36x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedTaskForm } from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';
import { isDateBeforeYesterday } from '../helpers/due-date.helper';

const EndsOnNever = observer(() => {
    return (
        <Box pt="2x" data-id="Z8j4bNmo">
            <RadioField
                formId={TASKS_FORM_ID}
                label={t`Never`}
                name="scheduleEndNever"
                value="never"
                data-id="hATCzVeL"
                checked={
                    sharedTaskForm.currentValues.scheduleEndsOn === 'NEVER'
                }
                onChange={() => {
                    sharedTaskForm.update('scheduleEndsOn')('NEVER');
                }}
            />
        </Box>
    );
});

const EndsOnDate = observer(() => {
    return (
        <Stack direction="row" gap="2x" data-id="uJf3dS2-" align="center">
            <Box>
                <RadioField
                    formId={TASKS_FORM_ID}
                    label={t`On date`}
                    name="scheduleEndOnDate"
                    value="onDate"
                    data-id="gJf3dS2-"
                    checked={
                        sharedTaskForm.currentValues.scheduleEndsOn === 'ON'
                    }
                    onChange={() => {
                        sharedTaskForm.update('scheduleEndsOn')('ON');
                    }}
                />
            </Box>
            <Box
                width={dimension36x}
                onFocus={() => {
                    sharedTaskForm.update('scheduleEndsOn')('ON');
                }}
            >
                <DatePickerField
                    shouldHideLabel
                    formId={TASKS_FORM_ID}
                    label={t`End date`}
                    name="scheduleEnd"
                    locale="en-US"
                    getIsDateUnavailable={isDateBeforeYesterday}
                    dateUnavailableText={t`End date not available`}
                    monthSelectionFieldLabel={t`Select month`}
                    yearSelectionFieldLabel={t`Select year`}
                    value={
                        sharedTaskForm.currentValues.scheduleEnd as TDateISODate
                    }
                    onChange={(date) => {
                        sharedTaskForm.update('scheduleEndsOn')('ON');
                        sharedTaskForm.update('scheduleEnd')(String(date));
                    }}
                />
            </Box>
        </Stack>
    );
});

const EndsOnOptions = observer(() => {
    return (
        <Stack direction="column" gap="2x" data-id="EndsOnOptions">
            <EndsOnNever />
            <EndsOnDate />
        </Stack>
    );
});

export const TaskModalFormScheduleEnd = observer(() => {
    const error = '';

    return (
        <FormField
            formId={TASKS_FORM_ID}
            label={t`Ends on`}
            name="scheduleEnd"
            renderInput={() => <EndsOnOptions data-id="phPUbEAr" />}
            data-id="pJFc4s4t"
            feedback={{
                type: 'error',
                message: error,
            }}
        />
    );
});
