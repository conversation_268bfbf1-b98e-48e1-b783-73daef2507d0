import { sharedUsersInfiniteController } from '@controllers/users';
import { ComboboxField } from '@cosmos/components/combobox-field';
import {
    DatePickerField,
    type TDateISODate,
} from '@cosmos/components/date-picker-field';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { TextareaField } from '@cosmos/components/textarea-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { sharedTaskForm } from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';
import { isDateBeforeYesterday } from '../helpers/due-date.helper';

export const TaskModalFormDetails = observer(() => {
    const { currentValues: values } = sharedTaskForm;

    return (
        <>
            <Text as="h2" type="title" size="300">
                {t`Task details`}
            </Text>
            <TextField
                formId={TASKS_FORM_ID}
                label={t`Title`}
                name="title"
                value={values.title}
                onChange={sharedTaskForm.onChange('title')}
            />
            <TextareaField
                formId={TASKS_FORM_ID}
                label={t`Description`}
                name="description"
                maxCharacters={30000}
                rows={2}
                value={values.description ?? ''}
                onChange={sharedTaskForm.onChange('description')}
            />
            <ComboboxField
                key={values.assignee?.id ?? 'assignee'}
                formId={TASKS_FORM_ID}
                label={t`Task Owner`}
                name="assignee"
                placeholder={t`Search for personnel...`}
                loaderLabel={t`Loading personnel...`}
                getSearchEmptyState={() => t`No personnel found`}
                clearSelectedItemButtonLabel="Clear"
                defaultValue={toJS(values.assignee) ?? undefined}
                hasMore={sharedUsersInfiniteController.hasNextPage}
                isLoading={sharedUsersInfiniteController.isLoading}
                options={
                    sharedUsersInfiniteController.usersInfiniteListOptionsWithEntryId
                }
                onChange={sharedTaskForm.update('assignee')}
                onFetchOptions={sharedUsersInfiniteController.onFetchUsers}
            />
            <DatePickerField
                key={values.dueDate ?? 'dueDate'}
                formId={TASKS_FORM_ID}
                label={t`Due Date`}
                name="dueDate"
                value={values.dueDate as TDateISODate}
                locale="en-US"
                getIsDateUnavailable={isDateBeforeYesterday}
                dateUnavailableText={t`Task due date not available`}
                monthSelectionFieldLabel={t`Select month`}
                yearSelectionFieldLabel={t`Select year`}
                onChange={(date) => {
                    sharedTaskForm.updateDueDate(String(date));
                }}
            />
        </>
    );
});
