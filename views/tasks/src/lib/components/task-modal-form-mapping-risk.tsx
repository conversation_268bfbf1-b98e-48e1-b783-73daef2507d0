import { RiskDetailsAccordionComponent } from '@components/risk-details-accordion';
import { sharedRisksInfiniteListController } from '@controllers/risk';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { CustomTaskFullResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { mapRiskData, sharedTaskForm } from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

export const TaskModalFormMappingRisk = observer(() => {
    const {
        currentValues: { taskType, risk },
    } = sharedTaskForm;

    const {
        risksComboboxOptions,
        risksInfiniteList,
        hasNextPage,
        isLoading,
        onFetchRisks,
    } = sharedRisksInfiniteListController;

    const setRisk = (value: ListBoxItemData | undefined): void => {
        if (!value) {
            sharedTaskForm.update('risk')(undefined);

            return;
        }

        const riskData = risksInfiniteList.find(
            (c) => c.id === Number(value.id),
        );

        const mappedRisk = mapRiskData(
            riskData as unknown as CustomTaskFullResponseDto['risk'],
        );

        sharedTaskForm.update('risk')(mappedRisk);
    };

    const onRemoveRisk = () => {
        sharedTaskForm.update('risk')(null);
        sharedTaskForm.update('taskType')('GENERAL');
    };

    if (taskType !== 'RISK') {
        return null;
    }

    return (
        <>
            <ComboboxField
                key={risk?.id ?? 'task-risk'}
                formId={TASKS_FORM_ID}
                name="risk"
                label={t`Select risk`}
                helpText={t`Tasks can only be mapped to a single risk`}
                loaderLabel={t`Loading risks...`}
                defaultValue={toJS(risk) ?? undefined}
                options={risksComboboxOptions}
                hasMore={hasNextPage}
                isLoading={isLoading}
                onFetchOptions={onFetchRisks}
                onChange={setRisk}
            />
            {risk && (
                <RiskDetailsAccordionComponent
                    id={risk.id}
                    data-id={`task-modal-form-mapping-risk-${risk.id}`}
                    title={risk.title}
                    description={risk.description}
                    owners={risk.owners}
                    onRemoveRisk={onRemoveRisk}
                />
            )}
        </>
    );
});
