import { Box } from '@cosmos/components/box';
import { FormField } from '@cosmos/components/form-field';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import {
    getTaskFrequencyOptions,
    sharedTaskForm,
    type TaskScheduleType,
} from '@models/tasks';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

const ScheduleFrequency = observer(() => {
    const TASK_FREQUENCY_OPTIONS = getTaskFrequencyOptions();

    return (
        <Stack direction="row" gap="2x" data-id="uJf3dS2-">
            <Box width="16x">
                <TextField
                    shouldHideLabel
                    formId={TASKS_FORM_ID}
                    label={t`Interval`}
                    name="scheduleInterval"
                    value={sharedTaskForm.currentValues.scheduleInterval}
                    onChange={sharedTaskForm.onChange('scheduleInterval')}
                />
            </Box>
            <SelectField
                shouldHideLabel
                key={sharedTaskForm.currentValues.scheduleType?.value}
                label={t`Frequency`}
                formId={TASKS_FORM_ID}
                name="scheduleType"
                options={TASK_FREQUENCY_OPTIONS}
                value={toJS(sharedTaskForm.currentValues.scheduleType)}
                fieldWidth="fit-content"
                onChange={(type) => {
                    sharedTaskForm.update('scheduleType')(
                        type as TaskScheduleType,
                    );
                }}
            />
        </Stack>
    );
});

export const TaskModalFormScheduleInterval = observer(() => {
    const error = '';

    return (
        <FormField
            formId={TASKS_FORM_ID}
            label={t`Repeat every`}
            name="scheduleFrequency"
            renderInput={() => <ScheduleFrequency data-id="keXSsZQ4" />}
            data-id="smVuf8Wn"
            feedback={{
                type: 'error',
                message: error,
            }}
        />
    );
});
