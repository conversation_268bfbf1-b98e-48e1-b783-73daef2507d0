import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedTaskForm } from '@models/tasks';

export const TaskModalFormScheduleOccurrence = observer(() => {
    return (
        <Box
            backgroundColor="primaryBackgroundMild"
            borderRadius="borderRadiusMd"
            data-id="usxUCD8r"
            px="1x"
            py="4x"
            width="100%"
        >
            <Stack direction="column" align="center" justify="center" gap="1x">
                <Text type="body" size="100">
                    {t`Next recurrence is scheduled for`}
                </Text>
                <Text type="title" size="200">
                    {sharedTaskForm.nextOccurrence}
                </Text>
            </Stack>
        </Box>
    );
});
