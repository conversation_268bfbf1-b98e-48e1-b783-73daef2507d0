import { sharedAuditHubControlDetailsController } from '@controllers/audit-hub-control-details';
import { sharedControlOwnersController } from '@controllers/controls';
import { Metadata } from '@cosmos/components/metadata';
import {
    PanelBody,
    PanelControls,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { getUserInitials } from '@helpers/user';
import { sharedControlDetailsPanelController } from '../controllers';
import { ControlEvidenceSection } from './control-evidence-section';

interface ControlDetailsPanelProps {
    'data-id'?: string;
}

export const ControlDetailsPanel = observer(
    ({ 'data-id': dataId }: ControlDetailsPanelProps): React.JSX.Element => {
        const { controlOwners } = sharedControlOwnersController;
        const { controlDetails } = sharedAuditHubControlDetailsController;
        const {
            hasValidControlData,
            currentControlPosition,
            totalControls,
            handleClosePanel,
            handleNextPage,
            handlePrevPage,
            currentControlData,
        } = sharedControlDetailsPanelController;

        if (!hasValidControlData || !controlDetails) {
            return (
                <Stack
                    align="center"
                    justify="center"
                    height="100%"
                    width="100%"
                >
                    <Text>{t`Control not found`}</Text>
                </Stack>
            );
        }

        return (
            <Stack
                data-testid="ControlDetailsPanel"
                data-id={dataId}
                direction="column"
            >
                <PanelControls
                    closeButtonLabel={t`Close`}
                    data-id="control-details-panel-controls"
                    pagination={{
                        currentItem: currentControlPosition,
                        totalItems: totalControls,
                        onNextPageClick: handleNextPage,
                        onPrevPageClick: handlePrevPage,
                    }}
                    onClose={handleClosePanel}
                />
                <PanelHeader
                    data-id="control-details-panel-header"
                    title={controlDetails.name}
                    slot={
                        <Metadata
                            label={controlDetails.code}
                            type="tag"
                            data-id="control-details-panel-code-metadata"
                        />
                    }
                />
                <PanelBody data-id="control-details-panel-body">
                    <Stack gap="4x" direction="column">
                        <Stack gap="2x" direction="column">
                            <Text type="headline" size="300">
                                {t`Control details`}
                            </Text>

                            <Stack gap="2xl" direction="column">
                                {/* Owners section */}
                                <Stack gap="1x" direction="column">
                                    <Text
                                        type="title"
                                        size="100"
                                        colorScheme="neutral"
                                    >
                                        {t`Owners`}
                                    </Text>
                                    <AvatarStack
                                        maxVisibleItems={3}
                                        data-id="control-owners-avatar-stack"
                                        avatarData={controlOwners.map(
                                            (owner) => ({
                                                fallbackText: getUserInitials({
                                                    firstName: owner.firstName,
                                                    lastName: owner.lastName,
                                                }),
                                                primaryLabel: getFullName(
                                                    owner.firstName,
                                                    owner.lastName,
                                                ),
                                                imgSrc:
                                                    owner.avatarUrl ||
                                                    undefined,
                                            }),
                                        )}
                                    />
                                </Stack>

                                {/* Name */}
                                <Stack gap="1x" direction="column">
                                    <Text
                                        type="title"
                                        size="100"
                                        colorScheme="neutral"
                                    >
                                        {t`Name`}
                                    </Text>
                                    <Text>{controlDetails.name}</Text>
                                </Stack>

                                {/* Code */}
                                <Stack gap="1x" direction="column">
                                    <Text
                                        type="title"
                                        size="100"
                                        colorScheme="neutral"
                                    >
                                        {t`Code`}
                                    </Text>
                                    <Text>{controlDetails.code}</Text>
                                </Stack>

                                {/* Description */}
                                {controlDetails.description && (
                                    <Stack gap="1x" direction="column">
                                        <Text
                                            type="title"
                                            size="100"
                                            colorScheme="neutral"
                                        >
                                            {t`Description`}
                                        </Text>
                                        <Text>
                                            {controlDetails.description}
                                        </Text>
                                    </Stack>
                                )}

                                {/* Question */}
                                {controlDetails.question && (
                                    <Stack gap="1x" direction="column">
                                        <Text
                                            type="title"
                                            size="100"
                                            colorScheme="neutral"
                                        >
                                            {t`Question`}
                                        </Text>
                                        <Text>{controlDetails.question}</Text>
                                    </Stack>
                                )}

                                {/* Activities */}
                                {controlDetails.activity && (
                                    <Stack gap="1x" direction="column">
                                        <Text
                                            type="title"
                                            size="100"
                                            colorScheme="neutral"
                                        >
                                            {t`Activities`}
                                        </Text>
                                        <Text>{controlDetails.activity}</Text>
                                    </Stack>
                                )}
                            </Stack>
                        </Stack>

                        {currentControlData.quantityOfValidEvidence > 0 && (
                            <Stack gap="2x" direction="column">
                                <ControlEvidenceSection data-id="control-evidence-section" />
                            </Stack>
                        )}
                    </Stack>
                </PanelBody>
            </Stack>
        );
    },
);
