import { AppDatatable } from '@components/app-datatable';
import { openLinkControlsModalWithWorkspace } from '@components/evidence-library';
import {
    sharedAuditHubController,
    sharedAuditHubControlsController,
} from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getEvidenceRequestDetailsControlsColumns } from './constants/evidence-controls-columns';
import { openControlDetailsPanel } from './helpers/open-control-details-panel.helper';

export const EvidenceRequestDetailsControlsView = observer((): JSX.Element => {
    const {
        isEvidencePackageDownloading,
        downloadSelectedControls,
        downloadAllControls,
        handleRowSelection,
        updateRequestControls,
    } = sharedCustomerRequestDetailsController;

    const {
        auditCustomerRequestControls,
        auditCustomerRequestControlsIsLoading,
        auditCustomerRequestControlsTotal,
        loadControlsPage,
    } = sharedAuditHubControlsController;

    const { auditByIdData } = sharedAuditHubController;
    const workspaceId = auditByIdData?.framework.productId;
    const { getRequestId: requestId, auditorFrameworkId: auditId } =
        sharedCustomerRequestDetailsController;

    return (
        <AppDatatable
            isRowSelectionEnabled
            hidePagination
            getRowId={(row) => String(row.id)}
            isLoading={auditCustomerRequestControlsIsLoading}
            tableId="datatable-audit-hub-evidence-request-details-controls"
            data={auditCustomerRequestControls}
            total={auditCustomerRequestControlsTotal}
            data-testid="EvidenceRequestDetailsControlsView"
            data-id="U9j_XYCl"
            columns={getEvidenceRequestDetailsControlsColumns()}
            defaultColumnOptions={{
                minSize: 5,
            }}
            defaultPaginationOptions={{
                pageSizeOptions: [5, 10, 20, 50],
                pageSize: 10,
                pageIndex: 0,
            }}
            bulkActionDropdownItems={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        label: t`Download Selection`,
                        level: 'tertiary',
                        onClick: downloadSelectedControls,
                    },
                },
            ]}
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: false,
                        label: t`Download all`,
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        isLoading: isEvidencePackageDownloading,
                        a11yLoadingLabel: t`Downloading evidence package`,
                        onClick: downloadAllControls,
                    },
                },
                {
                    actionType: 'button',
                    id: 'map-controls-button',
                    typeProps: {
                        label: t`Map controls`,
                        level: 'secondary',
                        onClick: () => {
                            const currentControlIds: number[] = [];

                            openLinkControlsModalWithWorkspace({
                                objectType: 'risk',
                                onConfirm: (selectedControls) => {
                                    const controlIds = selectedControls.map(
                                        (item) => item.controlData.id,
                                    );

                                    updateRequestControls(
                                        controlIds as number[],
                                    );
                                },
                                excludeControlIds: currentControlIds,
                                auditId: auditId || undefined,
                                productId: workspaceId,
                                excludeRequestId: requestId || undefined,
                            });
                        },
                    },
                },
            ]}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Controls`,
                description: t`No controls were found`,
            }}
            onFetchData={loadControlsPage}
            onRowSelection={handleRowSelection}
            onRowClick={({ row }) => {
                openControlDetailsPanel(row);
            }}
        />
    );
});
