import { isArray, isEmpty } from 'lodash-es';
import { useCallback, useState } from 'react';
import {
    activeAccessReviewApplicationDetailsController,
    sharedAccessReviewApplicationReviewersController,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { ComboboxField } from '@cosmos/components/combobox-field';
import {
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
} from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { ShowMore } from '@cosmos-lab/components/show-more';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

const MAX_USERS_TO_DISPLAY = 5;
const FORM_ID = 'editing-reviewers';

const getReviewers = (applicationReviewers: UserResponseDto[]) => {
    return (
        <StackedList data-id="V0smpIue" data-testid="getReviewers">
            {applicationReviewers.map(
                ({ firstName, lastName, id, avatarUrl }) => {
                    const fullName = getFullName(firstName, lastName);
                    const fallbackText = getInitials(fullName);

                    return (
                        <StackedListItem
                            data-id="XJzjA1c4"
                            key={`${id}-${firstName}-${lastName}`}
                            primaryColumn={
                                <Stack
                                    direction="row"
                                    align="center"
                                    gap="2x"
                                    key={`${id}-${firstName}-${lastName}`}
                                    data-id="V0smpIue"
                                >
                                    <AvatarIdentity
                                        data-id="eVtVN8Z-"
                                        primaryLabel={fullName}
                                        fallbackText={fallbackText}
                                        imgSrc={avatarUrl ?? undefined}
                                    />
                                </Stack>
                            }
                        />
                    );
                },
            )}
        </StackedList>
    );
};

const getReviewerData = (
    applicationReviewers: UserResponseDto[],
): ListBoxItemData[] => {
    return applicationReviewers.map((reviewer) => ({
        avatar: {
            fallbackText: getInitials(
                getFullName(reviewer.firstName, reviewer.lastName),
            ),
            imgAlt: getFullName(reviewer.firstName, reviewer.lastName),
        },
        id: String(reviewer.id),
        label: getFullName(reviewer.firstName, reviewer.lastName),
        value: String(reviewer.id),
    }));
};

export const ReviewersCard = observer((): React.JSX.Element => {
    const [isEditing, setIsEditing] = useState(false);
    const [feedback, setFeedback] = useState<
        { type: 'error' | 'success'; message: string } | undefined
    >(undefined);
    const { applicationReviewers } =
        activeAccessReviewApplicationDetailsController;
    const [selectedReviewers, setSelectedReviewers] = useState<
        ListBoxItemData[] | ListBoxItemData
    >(getReviewerData(applicationReviewers));

    const {
        hasNextPage: hasMoreUsers,
        isLoading,
        isFetching,
        onFetchUsers,
        options,
    } = sharedUsersInfiniteController;

    const {
        totalReviewApplicationUsers,
        loadAccessReviewPeriodApplicationUsers,
    } = sharedAccessReviewPeriodApplicationUsersController;

    const { hasLimitedAccess } = sharedFeatureAccessModel;

    const handleOnChange = useCallback(
        (selectedItems: ListBoxItemData[] | ListBoxItemData) => {
            setSelectedReviewers(selectedItems);
            setFeedback(undefined);
            if (isArray(selectedItems) && selectedItems.length === 1) {
                loadAccessReviewPeriodApplicationUsers({
                    pagination: {
                        pageIndex: DEFAULT_PAGE_INDEX,
                        pageSize: DEFAULT_PAGE_SIZE,
                        pageSizeOptions: [DEFAULT_PAGE_SIZE],
                    },
                    globalFilter: {
                        search: selectedItems[0].label,
                        filters: {},
                    },
                    sorting: [],
                });
            }
        },
        [setSelectedReviewers, loadAccessReviewPeriodApplicationUsers],
    );

    const handleOnSave = useCallback(() => {
        const isAvailableReviewers =
            isArray(selectedReviewers) &&
            selectedReviewers.length === 1 &&
            totalReviewApplicationUsers > 0;

        if (isArray(selectedReviewers) && isEmpty(selectedReviewers)) {
            setFeedback({
                type: 'error',
                message: t`Application must have at least 1 reviewer`,
            });

            return;
        }

        if (isAvailableReviewers) {
            setFeedback({
                type: 'error',
                message: t`Reviewers cannot review their own status. Please add another reviewer.`,
            });

            return;
        }

        sharedAccessReviewApplicationReviewersController.updateApplicationReviewers(
            (selectedReviewers as ListBoxItemData[]).map((reviewer) =>
                Number(reviewer.id),
            ),
            () => {
                sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
                activeAccessReviewApplicationDetailsController.refreshUsersReviewers();
            },
        );
        setIsEditing(false);
    }, [selectedReviewers, totalReviewApplicationUsers]);

    return (
        <Box data-id="linked-evidence-card" height="fit-content">
            <Card
                title={t`Reviewers`}
                size="lg"
                isEditMode={isEditing}
                body={
                    isEditing ? (
                        <Stack direction="column" gap="6x">
                            <ComboboxField
                                isMultiSelect
                                isLoading={isLoading && isFetching}
                                clearSelectedItemButtonLabel={t`Clear`}
                                data-id="reviewers-combobox"
                                formId={FORM_ID}
                                feedback={feedback}
                                placeholder={t`Search by name or email`}
                                label=""
                                hasMore={hasMoreUsers}
                                loaderLabel={t`Loading results`}
                                name="reviewers-combobox"
                                removeAllSelectedItemsLabel={t`Clear all`}
                                options={options}
                                defaultSelectedOptions={getReviewerData(
                                    applicationReviewers,
                                )}
                                getSearchEmptyState={() =>
                                    t`No employees found`
                                }
                                onChange={handleOnChange}
                                onFetchOptions={onFetchUsers}
                            />
                            <Stack gap="3x">
                                <Button
                                    label={t`Save`}
                                    level="primary"
                                    onClick={handleOnSave}
                                />
                                <Button
                                    label={t`Cancel`}
                                    level="secondary"
                                    onClick={() => {
                                        setFeedback(undefined);
                                        setIsEditing(false);
                                    }}
                                />
                            </Stack>
                        </Stack>
                    ) : (
                        <>
                            {applicationReviewers.length >
                            MAX_USERS_TO_DISPLAY ? (
                                <ShowMore
                                    content={getReviewers(applicationReviewers)}
                                />
                            ) : (
                                getReviewers(applicationReviewers)
                            )}
                        </>
                    )
                }
                actions={
                    hasLimitedAccess || isEditing
                        ? []
                        : [
                              {
                                  actionType: 'button',
                                  id: 'edit-button',
                                  typeProps: {
                                      label: t`Edit`,
                                      level: 'secondary',
                                      'data-id': 'edit-button',
                                      onClick: () => {
                                          setIsEditing(true);
                                      },
                                  },
                              },
                          ]
                }
            />
        </Box>
    );
});
