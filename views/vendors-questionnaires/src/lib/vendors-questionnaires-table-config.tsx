import {
    VENDOR_QUESTIONNAIRE_CATEGORY_OPTIONS,
    VENDOR_QUESTIONNAIRE_STATUS_OPTIONS,
    VENDOR_RISK_LIST_OPTION,
} from '@components/vendor-questionnaires';
import type { FilterProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getVendorsQuestionnairesFilters = (): FilterProps => ({
    clearAllButtonLabel: t`Reset`,
    filters: [
        {
            filterType: 'select',
            id: 'vendors-questionnaires-table-filter-status',
            label: t`Status`,
            options: VENDOR_QUESTIONNAIRE_STATUS_OPTIONS,
        },
        {
            filterType: 'select',
            id: 'vendors-questionnaires-table-filter-category',
            label: t`Category`,
            options: VENDOR_QUESTIONNAIRE_CATEGORY_OPTIONS,
        },
        {
            filterType: 'select',
            id: 'vendors-questionnaires-table-filter-risk',
            label: t`Risk level`,
            options: VENDOR_RISK_LIST_OPTION,
        },
    ],
});
