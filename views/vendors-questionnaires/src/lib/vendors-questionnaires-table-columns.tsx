import {
    type VendorQuestionnaireRowData,
    VendorsQuestionnairesCellCategoriesComponent,
    VendorsQuestionnairesCellLastUsedComponent,
    VendorsQuestionnairesCellRiskLevelComponent,
    VendorsQuestionnairesCellStatusComponent,
} from '@components/vendor-questionnaires';
import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getVendorsQuestionnairesColumns =
    (): DatatableProps<VendorQuestionnaireRowData>['columns'] => [
        {
            id: 'title',
            header: t`Name`,
            enableSorting: true,
            accessorKey: 'title',
            maxSize: 200,
        },
        {
            id: 'status',
            header: t`Status`,
            enableSorting: true,
            accessorKey: 'status',
            maxSize: 100,
            cell: VendorsQuestionnairesCellStatusComponent,
        },
        {
            id: 'categories',
            header: t`Categories`,
            enableSorting: true,
            accessorKey: 'categories',
            cell: VendorsQuestionnairesCellCategoriesComponent,
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            id: 'riskLevels',
            header: t`Risk levels`,
            enableSorting: true,
            accessorKey: 'riskLevels',
            cell: VendorsQuestionnairesCellRiskLevelComponent,
        },
        {
            id: 'lastUsed',
            header: t`Last used`,
            enableSorting: true,
            accessorKey: 'updatedAt',
            cell: VendorsQuestionnairesCellLastUsedComponent,
        },
    ];
