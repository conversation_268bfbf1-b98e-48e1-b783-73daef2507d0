import { noop } from 'lodash-es';
import type { ComponentProps } from 'react';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export const VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY =
    'vendors-questionnaires-header';

export const getVendorsQuestionnairesPageHeaderActions = (
    navigate: (path: string) => void,
): ComponentProps<typeof ActionStack>['actions'] => [
    {
        actionType: 'dropdown',
        id: `${VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY}-add-action`,
        typeProps: {
            label: t`Add questionnaire`,
            endIconName: 'ChevronDown',
            align: 'end',

            items: [
                {
                    id: `${VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY}-dropdown-from-scratch`,
                    label: t`From scratch`,
                    type: 'item',
                    value: 'ADD_SINGLE',
                    description: t`Create from a list of question types`,
                    onSelect: () => {
                        runInAction(() => {
                            const { currentWorkspace } =
                                sharedWorkspacesController;

                            if (!currentWorkspace?.id) {
                                return;
                            }

                            const workspaceId = currentWorkspace.id;

                            navigate(
                                `/workspaces/${workspaceId}/vendors/questionnaires/add`,
                            );
                        });
                    },
                },
                {
                    id: `${VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY}-dropdown-import-questions`,
                    label: t`Import questions`,
                    type: 'item',
                    value: 'ADD_BULK',
                    description: t`Upload questions in bulk using a template`,
                    onSelect: () => {
                        // TODO: Implement import questions https://drata.atlassian.net/browse/ENG-68446
                        noop();
                    },
                },
            ],
        },
    },
];
