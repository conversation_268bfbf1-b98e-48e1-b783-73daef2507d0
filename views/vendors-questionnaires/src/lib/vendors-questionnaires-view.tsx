import type { VendorQuestionnaireRowData } from '@components/vendor-questionnaires';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { Datatable } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { getVendorsQuestionnairesColumns } from './vendors-questionnaires-table-columns';
import { getVendorsQuestionnairesFilters } from './vendors-questionnaires-table-config';

export const VendorsQuestionnairesView = observer((): React.JSX.Element => {
    const { allVendorsQuestionnaires, isLoading, total, loadQuestionnaires } =
        sharedVendorsTypeformQuestionnairesController;
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleRowClick = ({ row }: { row: VendorQuestionnaireRowData }) => {
        const { id } = row;

        if (currentWorkspace?.id && id) {
            navigate(
                `/workspaces/${currentWorkspace.id}/vendors/questionnaires/${id}`,
            );
        }
    };

    return (
        <Datatable
            isLoading={isLoading}
            tableId="datatable-vendors-questionnaires"
            data={allVendorsQuestionnaires}
            data-id="datatable-vendors-questionnaires-data-id"
            columns={getVendorsQuestionnairesColumns()}
            total={total}
            filterProps={getVendorsQuestionnairesFilters()}
            tableSearchProps={{
                placeholder: t`Search`,
                hideSearch: false,
                debounceDelay: 500,
                defaultValue: '',
            }}
            onRowClick={handleRowClick}
            onFetchData={loadQuestionnaires}
        />
    );
});
