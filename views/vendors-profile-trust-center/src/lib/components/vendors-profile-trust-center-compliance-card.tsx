import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { VendorsComplianceFramework } from './vendors-compliance-framework';

export const VendorsProfileTrustCenterComplianceCard = observer(
    (): React.JSX.Element => {
        const { isCertificationsLoading, certifications } =
            sharedVendorTrustCenterController;

        return (
            <Card
                isLoading={isCertificationsLoading}
                size="lg"
                title="Compliance"
                data-testid="VendorsProfileTrustCenterComplianceCard"
                data-id="JI-8N9r4"
                body={
                    <Stack gap="5xl" wrap="wrap" width="100%">
                        {certifications.map(({ id, name, imgUrl }) => (
                            <VendorsComplianceFramework
                                key={id}
                                name={name}
                                imgSrc={imgUrl}
                                data-id="WC5Bpmxg"
                            />
                        ))}
                    </Stack>
                }
            />
        );
    },
);
