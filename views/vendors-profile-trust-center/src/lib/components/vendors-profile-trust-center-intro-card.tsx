import type React from 'react';
import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { MarkdownViewer } from '@cosmos-lab/components/markdown-viewer';
import { observer } from '@globals/mobx';

export const VendorsProfileTrustCenterIntroCard = observer(
    (): React.JSX.Element => {
        const { isOverviewLoading, overview } =
            sharedVendorTrustCenterController;

        return (
            <Card
                isLoading={isOverviewLoading}
                size="lg"
                title="Introduction"
                data-testid="VendorsProfileTrustCenterIntroCard"
                data-id="XHHQLAYk"
                body={
                    <MarkdownViewer data-id="cPlbHXUK">
                        {overview ?? ''}
                    </MarkdownViewer>
                }
            />
        );
    },
);
