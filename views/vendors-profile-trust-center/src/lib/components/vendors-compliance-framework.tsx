import { Avatar } from '@cosmos/components/avatar';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension16x } from '@cosmos/constants/tokens';
import { getInitials } from '@helpers/formatters';

interface VendorsComplianceFrameworkProps {
    name: string;
    imgSrc: string;
}

export const VendorsComplianceFramework = ({
    name,
    imgSrc,
}: VendorsComplianceFrameworkProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            align="center"
            gap="sm"
            data-id="CdnCZzWt"
            data-testid="VendorsComplianceFramework"
            width={dimension16x}
        >
            <Avatar
                fallbackText={getInitials(name, { maxInitials: 4 })}
                imgSrc={imgSrc}
                imgAlt={name}
                size="lg"
            />
            <Text shouldWrap size="100" as="div" align="center">
                {name}
            </Text>
        </Stack>
    );
};
