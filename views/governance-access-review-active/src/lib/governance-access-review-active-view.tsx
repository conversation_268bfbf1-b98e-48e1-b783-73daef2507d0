import { isNil } from 'lodash-es';
import { useEffect, useState } from 'react';
import {
    AccessReviewActiveDataTable,
    AccessReviewPeriodRangeComponent,
    CompleteReviewModal,
} from '@components/access-review';
import { sharedActiveAccessReviewPeriodsController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { Banner } from '@cosmos/components/banner';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { compareDayIsPast, formatDate } from '@helpers/date-time';
import { useNavigate } from '@remix-run/react';

const COMPLETE_REVIEW_MODAL_ID = 'complete-review-modal';

interface GovernanceAccessReviewActivePresentationProps {
    isLoading: boolean;
    hasActivePeriod: boolean | undefined;
    onCreateReviewPeriod: () => void;
    onEditReviewPeriod: () => void;
    shouldOpenCompleteReviewModal: () => void;
    isDueDate: boolean;
    totalNotCompletedApplications: number;
}

const ReviewStatusBanners = ({
    isDueDate,
    shouldShowCompletedApplicationsBanner,
    totalNotCompletedApplications,
    isLoading,
}: {
    isDueDate: boolean;
    shouldShowCompletedApplicationsBanner: boolean;
    totalNotCompletedApplications: number;
    isLoading: boolean;
}): React.JSX.Element => (
    <>
        {isLoading ? undefined : (
            <>
                {isDueDate && (
                    <Banner
                        displayMode="section"
                        severity="critical"
                        title={t`This review period is overdue`}
                        body={t`Make sure reviewers have completed all user access reviews for each application before completing this review period.`}
                        data-testid="OverdueBanner"
                        data-id="overdue-banner"
                    />
                )}
                {shouldShowCompletedApplicationsBanner && (
                    <Banner
                        displayMode="section"
                        severity="warning"
                        title={t`Complete all application reviews`}
                        data-testid="IncompleteApplicationsBanner"
                        data-id="incomplete-apps-banner"
                        body={
                            totalNotCompletedApplications === 1
                                ? t`You have 1 application that has not been reviewed. Review it as soon as possible to be able to complete this review period.`
                                : t`You have ${totalNotCompletedApplications} applications that have not been reviewed. Review them as soon as possible to be able to complete this review period.`
                        }
                    />
                )}
            </>
        )}
    </>
);

const GovernanceAccessReviewActivePresentation = ({
    isLoading,
    hasActivePeriod,
    onCreateReviewPeriod,
    onEditReviewPeriod,
    isDueDate,
    totalNotCompletedApplications,
    shouldOpenCompleteReviewModal,
}: GovernanceAccessReviewActivePresentationProps): React.JSX.Element => {
    const [
        shouldShowCompletedApplicationsBanner,
        setShouldShowCompletedApplicationsBanner,
    ] = useState(false);

    const shouldOpenCompleteReviewModalHandler = (shouldShow: boolean) => {
        setShouldShowCompletedApplicationsBanner(shouldShow);

        if (shouldShow) {
            return;
        }

        shouldOpenCompleteReviewModal();
    };

    return (
        <Stack
            py="8x"
            direction="column"
            gap="xl"
            data-testid="GovernanceAccessReviewActivePresentation"
            data-id="yzcfZDE1"
        >
            <ReviewStatusBanners
                isDueDate={isDueDate}
                isLoading={isLoading}
                totalNotCompletedApplications={totalNotCompletedApplications}
                shouldShowCompletedApplicationsBanner={
                    shouldShowCompletedApplicationsBanner
                }
            />
            <AccessReviewPeriodRangeComponent
                totalNotCompletedApplications={totalNotCompletedApplications}
                hasActivePeriod={hasActivePeriod}
                shouldOpenCompleteReviewModal={
                    shouldOpenCompleteReviewModalHandler
                }
            />
            <AccessReviewActiveDataTable
                hasActivePeriod={hasActivePeriod}
                onCreateReviewPeriod={onCreateReviewPeriod}
                onEditReviewPeriod={onEditReviewPeriod}
            />
        </Stack>
    );
};

export const GovernanceAccessReviewActiveView = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const { isLoading, hasActivePeriod, activeAccessReviewPeriod } =
            sharedActiveAccessReviewPeriodsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        useEffect(() => {
            action(() => {
                sharedActiveAccessReviewPeriodsController.resetPagination();
            })();
        }, []);

        const totalNotCompletedApplicationsFiltered =
            activeAccessReviewPeriod?.applications.filter(
                (app) => app.status !== 'COMPLETED' && app.status !== 'DELETED',
            ).length ?? 0;

        const isActivePeriodInDueDate = isNil(activeAccessReviewPeriod?.endDate)
            ? false
            : compareDayIsPast(activeAccessReviewPeriod.endDate, new Date());

        const handleCreateReviewPeriod = (): void => {
            navigate(
                `/workspaces/${currentWorkspaceId}/governance/access-review/create-period`,
            );
        };

        const openReviewModalHandler = (): void => {
            if (!activeAccessReviewPeriod) {
                console.error('No active access review period found');

                return;
            }

            // Format the review period range for the modal
            const reviewPeriodRange =
                activeAccessReviewPeriod.startDate &&
                activeAccessReviewPeriod.endDate
                    ? formatDate(
                          'sentence_range',
                          activeAccessReviewPeriod.startDate.split('T')[0],
                          activeAccessReviewPeriod.endDate.split('T')[0],
                      )
                    : '-';

            modalController.openModal({
                id: COMPLETE_REVIEW_MODAL_ID,
                content: () => (
                    <CompleteReviewModal
                        modalId={COMPLETE_REVIEW_MODAL_ID}
                        reviewPeriodRange={reviewPeriodRange}
                        data-id="tTj7yfj1"
                    />
                ),
                centered: true,
                disableClickOutsideToClose: false,
            });
        };

        const handleEditReviewPeriod = (): void => {
            navigate(
                `/workspaces/${currentWorkspaceId}/governance/access-review/edit-period/${activeAccessReviewPeriod?.id}`,
            );
        };

        return (
            <GovernanceAccessReviewActivePresentation
                isLoading={isLoading}
                hasActivePeriod={hasActivePeriod}
                data-id="das1QBB-"
                isDueDate={isActivePeriodInDueDate}
                shouldOpenCompleteReviewModal={openReviewModalHandler}
                totalNotCompletedApplications={
                    totalNotCompletedApplicationsFiltered
                }
                onCreateReviewPeriod={handleCreateReviewPeriod}
                onEditReviewPeriod={handleEditReviewPeriod}
            />
        );
    },
);
