import { modalController } from '@controllers/modal';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { VendorsMarkVendorAsActiveView } from '../lib/vendors-mark-vendor-as-active-view';

export const openMarkVendorAsActiveModal = (): void => {
    sharedVendorsTypeformQuestionnairesController.loadQuestionnaires({
        pagination: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
        globalFilter: { search: '', filters: {} },
    } as FetchDataResponseParams);

    modalController.openModal({
        id: 'mark-vendor-as-active-modal',
        content: () => <VendorsMarkVendorAsActiveView data-id="87RwD6dm" />,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
};

export const closeMarkVendorAsActiveModal = (): void => {
    modalController.closeModal('mark-vendor-as-active-modal');
};
