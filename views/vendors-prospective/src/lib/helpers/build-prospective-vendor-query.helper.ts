import { isEmpty, isString } from 'lodash-es';
import { PROSPECTIVE_COLUMN_NAMES_TO_SORT_IDS_MAP } from '@controllers/vendors';
import type { VendorsControllerGetProspectiveReportData } from '@globals/api-sdk/types';
// eslint-disable-next-line no-restricted-imports -- Only allowed for type imports
import type { SortingState } from '@tanstack/react-table';

/**
 * Builds the query parameters for prospective vendor report download.
 */
export const buildProspectiveVendorQuery = (
    search: string,
    filters: Record<string, unknown>,
    sorting: SortingState,
): VendorsControllerGetProspectiveReportData['query'] => {
    const { impactLevel, reviewStatus } = filters;

    // Build the base query - use the original type to allow undefined values
    const query: VendorsControllerGetProspectiveReportData['query'] = {};

    // Add search query if present
    if (search) {
        query.q = search;
    }

    // Add impact level if present and valid
    if (impactLevel && isString(impactLevel)) {
        query.impactLevel = impactLevel as NonNullable<
            VendorsControllerGetProspectiveReportData['query']
        >['impactLevel'];
    }

    // Add review status if present and valid
    if (reviewStatus && isString(reviewStatus)) {
        query.reviewStatus = reviewStatus as NonNullable<
            VendorsControllerGetProspectiveReportData['query']
        >['reviewStatus'];
    }

    // Add sorting if present
    if (!isEmpty(sorting) && sorting[0]?.id) {
        const sortId = sorting[0].id;

        if (sortId in PROSPECTIVE_COLUMN_NAMES_TO_SORT_IDS_MAP) {
            const mappedSort =
                PROSPECTIVE_COLUMN_NAMES_TO_SORT_IDS_MAP[
                    sortId as keyof typeof PROSPECTIVE_COLUMN_NAMES_TO_SORT_IDS_MAP
                ];

            query.sort = mappedSort;
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }
    }

    return query;
};
