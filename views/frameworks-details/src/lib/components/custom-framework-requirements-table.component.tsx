import { useCallback, useMemo, useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedCustomFrameworkRequirementsController } from '@controllers/frameworks';
import type { DatatableRef } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useLocation, useNavigate } from '@remix-run/react';
import { getFrameworksDetailsColumns } from '../../constants/columns.constant';
import {
    getRequirementsFirstTimeEmptyStateProps,
    getRequirementsNoResultsEmptyStateProps,
} from '../helpers/requirements-empty-state-helpers';
import { sharedCustomFrameworkRequirementsBulkActionsModel } from '../models/custom-framework-requirements-bulk-actions.model';
import { sharedFrameworkDetailsRequirementListModel } from '../models/framework-details-requirement-list-model';

export const CustomFrameworkRequirementsTable = observer(
    (): React.JSX.Element => {
        const datatableRef = useRef<DatatableRef>(null);
        const navigate = useNavigate();
        const location = useLocation();

        const { requirementListFilters, tableActions } =
            sharedFrameworkDetailsRequirementListModel;

        const {
            loadCustomFrameworkRequirements,
            requirements,
            customFrameworkRequirementsTotal,
            shouldShowFirstTimeEmpty,
            isLoading,
        } = sharedCustomFrameworkRequirementsController;

        const { handleRowSelection } =
            sharedCustomFrameworkRequirementsBulkActionsModel;

        const navigateToRequestDetail = useCallback(
            (requestId: number) => {
                navigate(`${location.pathname}/${requestId}`);
            },
            [location.pathname, navigate],
        );

        const columns = useMemo(() => getFrameworksDetailsColumns(), []);

        const emptyState = shouldShowFirstTimeEmpty
            ? getRequirementsFirstTimeEmptyStateProps()
            : getRequirementsNoResultsEmptyStateProps();

        return (
            <AppDatatable
                isRowSelectionEnabled
                isFullPageTable
                imperativeHandleRef={datatableRef}
                getRowId={(row) => String(row.id)}
                tableId="custom-requirements-list"
                isLoading={isLoading}
                total={customFrameworkRequirementsTotal}
                data={requirements}
                columns={columns}
                filterProps={requirementListFilters}
                tableActions={tableActions}
                data-id="CustomFrameworkRequirementsTable"
                emptyStateProps={emptyState}
                bulkActionDropdownItems={sharedCustomFrameworkRequirementsBulkActionsModel.bulkActions(
                    datatableRef,
                )}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onRowSelection={handleRowSelection}
                onFetchData={loadCustomFrameworkRequirements}
                onRowClick={({ row }) => {
                    navigateToRequestDetail(row.id);
                }}
            />
        );
    },
);
