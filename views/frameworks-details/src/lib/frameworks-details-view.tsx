import { observer } from '@globals/mobx';
import {
    CustomFrameworkRequirementsTable,
    NonCustomFrameworkRequirementsTable,
} from './components';
import { sharedFrameworkDetailsRequirementListModel } from './models/framework-details-requirement-list-model';

export const FrameworksDetailsView = observer((): React.JSX.Element => {
    const { isCustomFramework } = sharedFrameworkDetailsRequirementListModel;

    return isCustomFramework ? (
        <CustomFrameworkRequirementsTable />
    ) : (
        <NonCustomFrameworkRequirementsTable />
    );
});
