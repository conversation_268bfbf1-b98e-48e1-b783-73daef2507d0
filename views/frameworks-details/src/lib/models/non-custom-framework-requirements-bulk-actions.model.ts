import { isEmpty, isString } from 'lodash-es';
import { sharedRequirementsController } from '@controllers/requirements';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedRequirementsModel } from '@models/requirements';
import { openMarkRequirementOutOfScopeModal } from '../helpers/open-requirement-modals.helper';

export class NonCustomFrameworkRequirementsBulkActionsModel {
    selectedRequirementIds: number[] = [];
    isAllRowsSelected = false;

    constructor() {
        makeAutoObservable(this);
    }

    get currentScopeFilter(): string | undefined {
        // For non-custom frameworks, check the requirements controller
        const scopeValue = sharedRequirementsController.isInScope;

        return isString(scopeValue) ? scopeValue : undefined;
    }

    get isScopeFilterActive(): boolean {
        const scopeFilter = this.currentScopeFilter;

        return scopeFilter === 'true' || scopeFilter === 'false';
    }

    get isInScopeFilterActive(): boolean {
        return this.currentScopeFilter === 'true';
    }

    get isOutOfScopeFilterActive(): boolean {
        return this.currentScopeFilter === 'false';
    }

    bulkActions(tableRef: React.RefObject<DatatableRef>): BulkAction[] {
        sharedRequirementsModel.tableRef = tableRef;
        const actions: BulkAction[] = [];

        if (this.isScopeFilterActive) {
            const dropdownItems = [];

            // Add scope actions based on current filter
            if (this.isInScopeFilterActive) {
                dropdownItems.push({
                    id: 'bulk-mark-out-of-scope',
                    type: 'item',
                    label: t`Mark out of scope`,
                    value: 'mark-out-of-scope',
                });
            }

            if (this.isOutOfScopeFilterActive) {
                dropdownItems.push({
                    id: 'bulk-mark-in-scope',
                    type: 'item',
                    label: t`Mark in scope`,
                    value: 'mark-in-scope',
                });
            }

            // Only show the dropdown if we have items
            if (!isEmpty(dropdownItems)) {
                actions.push({
                    actionType: 'dropdown',
                    id: 'bulk-actions-dropdown',
                    typeProps: {
                        'data-id':
                            'non-custom-framework-requirements-bulk-actions',
                        label: t`Actions`,
                        level: 'tertiary',
                        items: dropdownItems,
                        onSelectGlobalOverride: ({ id }: { id: string }) => {
                            this.handleBulkAction(id);
                        },
                    },
                });
            }
        }

        return actions;
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        this.selectedRequirementIds = Object.entries(selectedRows)
            .filter(([, isSelected]) => isSelected)
            .map(([rowId]) => Number(rowId));
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleBulkAction = (actionId: string): void => {
        switch (actionId) {
            case 'bulk-mark-out-of-scope': {
                this.handleMarkOutOfScope();
                break;
            }
            case 'bulk-mark-in-scope': {
                this.handleMarkInScope();
                break;
            }
            default: {
                break;
            }
        }
    };

    handleMarkInScope = (): void => {
        sharedRequirementsController.updateRequirementsScope({
            requirementIds: this.selectedRequirementIds,
            isInScope: true,
            onSuccess: sharedRequirementsModel.resetRowSelection,
        });
    };

    handleMarkOutOfScope = (): void => {
        openMarkRequirementOutOfScopeModal(this.selectedRequirementIds);
    };
}

export const sharedNonCustomFrameworkRequirementsBulkActionsModel =
    new NonCustomFrameworkRequirementsBulkActionsModel();
