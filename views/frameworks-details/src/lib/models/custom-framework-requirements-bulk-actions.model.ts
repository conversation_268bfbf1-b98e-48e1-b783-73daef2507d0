import { isEmpty, isString } from 'lodash-es';
import { sharedCustomFrameworkRequirementsController } from '@controllers/frameworks';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedRequirementsModel } from '@models/requirements';
import {
    openChangeRequirementCategoryModal,
    openDeleteRequirementModal,
    openMarkRequirementOutOfScopeModal,
} from '../helpers/open-requirement-modals.helper';

export class CustomFrameworkRequirementsBulkActionsModel {
    selectedRequirementIds: number[] = [];
    isAllRowsSelected = false;

    constructor() {
        makeAutoObservable(this);
    }

    get currentScopeFilter(): string | undefined {
        // For custom frameworks, check the custom framework requirements controller
        const customScopeValue =
            sharedCustomFrameworkRequirementsController.isInScope;

        return isString(customScopeValue) ? customScopeValue : undefined;
    }

    get isScopeFilterActive(): boolean {
        const scopeFilter = this.currentScopeFilter;

        return scopeFilter === 'true' || scopeFilter === 'false';
    }

    get isInScopeFilterActive(): boolean {
        return this.currentScopeFilter === 'true';
    }

    get isOutOfScopeFilterActive(): boolean {
        return this.currentScopeFilter === 'false';
    }

    bulkActions(tableRef: React.RefObject<DatatableRef>): BulkAction[] {
        sharedRequirementsModel.customFrameworkTableRef = tableRef;
        const actions: BulkAction[] = [];

        if (this.isScopeFilterActive) {
            const dropdownItems = [];

            if (this.isInScopeFilterActive) {
                dropdownItems.push({
                    id: 'bulk-mark-out-of-scope',
                    type: 'item',
                    label: t`Mark out of scope`,
                    value: 'mark-out-of-scope',
                });
            }

            if (this.isOutOfScopeFilterActive) {
                dropdownItems.push({
                    id: 'bulk-mark-in-scope',
                    type: 'item',
                    label: t`Mark in scope`,
                    value: 'mark-in-scope',
                });
            }

            dropdownItems.push(
                {
                    id: 'bulk-change-category',
                    type: 'item',
                    label: t`Change category`,
                    value: 'change-category',
                },
                {
                    id: 'bulk-delete-requirements',
                    type: 'item',
                    label: t`Delete requirements`,
                    value: 'delete-requirements',
                    colorScheme: 'critical' as const,
                },
            );

            // Only show the dropdown if we have items
            if (!isEmpty(dropdownItems)) {
                actions.push({
                    actionType: 'dropdown',
                    id: 'bulk-actions-dropdown',
                    typeProps: {
                        'data-id': 'custom-framework-requirements-bulk-actions',
                        label: t`Actions`,
                        level: 'tertiary',
                        items: dropdownItems,
                        onSelectGlobalOverride: ({ id }: { id: string }) => {
                            this.handleBulkAction(id);
                        },
                    },
                });
            }
        } else {
            // When no scope filter is active, only show non-scope actions for custom frameworks
            const nonScopeItems = [
                {
                    id: 'bulk-change-category',
                    type: 'item',
                    label: t`Change category`,
                    value: 'change-category',
                },
                {
                    id: 'bulk-delete-requirements',
                    type: 'item',
                    label: t`Delete requirements`,
                    value: 'delete-requirements',
                    colorScheme: 'critical' as const,
                },
            ];

            actions.push({
                actionType: 'dropdown',
                id: 'bulk-actions-dropdown',
                typeProps: {
                    'data-id': 'custom-framework-requirements-bulk-actions',
                    label: t`Actions`,
                    level: 'tertiary',
                    items: nonScopeItems,
                    onSelectGlobalOverride: ({ id }: { id: string }) => {
                        this.handleBulkAction(id);
                    },
                },
            });
        }

        return actions;
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        this.selectedRequirementIds = Object.entries(selectedRows)
            .filter(([, isSelected]) => isSelected)
            .map(([rowId]) => Number(rowId));
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleBulkAction = (actionId: string): void => {
        const { requirements } = sharedCustomFrameworkRequirementsController;

        const frameworkId = requirements[0]?.frameworkId;

        if (!frameworkId) {
            return;
        }

        switch (actionId) {
            case 'bulk-mark-out-of-scope': {
                this.handleMarkOutOfScope();
                break;
            }
            case 'bulk-mark-in-scope': {
                this.handleMarkInScope();
                break;
            }
            case 'bulk-change-category': {
                openChangeRequirementCategoryModal(
                    this.selectedRequirementIds,
                    frameworkId,
                );
                break;
            }
            case 'bulk-delete-requirements': {
                openDeleteRequirementModal(this.selectedRequirementIds);
                break;
            }
            default: {
                break;
            }
        }
    };

    handleMarkInScope = (): void => {
        sharedCustomFrameworkRequirementsController.updateRequirementsScope({
            requirementIds: this.selectedRequirementIds,
            isInScope: true,
            onSuccess: sharedRequirementsModel.resetCustomFrameworkRowSelection,
        });
    };

    handleMarkOutOfScope = (): void => {
        openMarkRequirementOutOfScopeModal(this.selectedRequirementIds);
    };
}

export const sharedCustomFrameworkRequirementsBulkActionsModel =
    new CustomFrameworkRequirementsBulkActionsModel();
