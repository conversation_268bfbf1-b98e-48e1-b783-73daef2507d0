import {
    sharedCustomFrameworkRequirementsController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import { sharedRequirementsController } from '@controllers/requirements';
import type { RequirementListResponseDto } from '@globals/api-sdk/types';
import { action } from '@globals/mobx';

export const markRequirementsInScope = action(
    (requirements: RequirementListResponseDto[]): void => {
        const { frameworkDetails } = sharedFrameworkDetailsController;
        const isCustomFramework = frameworkDetails?.tag === 'CUSTOM';

        const requirementIds = requirements.map((req) => req.id);

        if (isCustomFramework) {
            // Use custom framework controller for custom frameworks
            sharedCustomFrameworkRequirementsController.updateRequirementsScope(
                {
                    requirementIds,
                    isInScope: true,
                },
            );
        } else {
            // Use requirements controller for non-custom frameworks
            sharedRequirementsController.updateRequirementsScope({
                requirementIds,
                isInScope: true,
            });
        }
    },
);

// Legacy single requirement function for backward compatibility
export const markRequirementInScope = action(
    (requirement: RequirementListResponseDto): void => {
        markRequirementsInScope([requirement]);
    },
);
