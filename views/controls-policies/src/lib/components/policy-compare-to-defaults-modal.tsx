import { isEmpty } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { Banner } from '@cosmos/components/banner';
import { Loader } from '@cosmos/components/loader';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Tabs } from '@cosmos/components/tabs';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedResetPoliciesModalModel } from '@models/controls';
import { PolicyListContent } from '.';

export const PoliciesCompareToDefaultsModal = observer(
    (): React.JSX.Element => {
        const {
            isLoading,
            isSaving,
            hasDefaultPolicies,
            policiesToAdd,
            policiesToRemove,
            policiesToAddCount,
            policiesToRemoveCount,
            defaultPoliciesLink,
        } = sharedResetPoliciesModalModel;

        const tabs = [
            {
                tabId: 'default-add-tab',
                label: t`Default would add (${policiesToAddCount})`,
                content: (
                    <PolicyListContent
                        policies={policiesToAdd}
                        data-id="policies-to-add"
                    />
                ),
            },
            {
                tabId: 'default-remove-tab',
                label: t`Default would remove (${policiesToRemoveCount})`,
                content: (
                    <PolicyListContent
                        policies={policiesToRemove}
                        data-id="policies-to-remove"
                    />
                ),
            },
        ];

        return (
            <>
                <Modal.Header
                    title={t`Comparison to default mapped policies`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={() => {
                        modalController.closeModal(
                            'policy-compare-to-defaults-modal',
                        );
                    }}
                />
                <Modal.Body>
                    {isLoading && (
                        <Stack justify="center" align="center" p="4x">
                            <Loader
                                isSpinnerOnly
                                size="md"
                                label={t`Loading...`}
                            />
                        </Stack>
                    )}
                    {!isLoading && (
                        <>
                            {!hasDefaultPolicies && (
                                <Stack direction="column" gap="4x">
                                    <Text as="div">
                                        <Trans>
                                            We periodically update{' '}
                                            <a
                                                href={defaultPoliciesLink}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                            >
                                                {`Drata's default mapped policies`}
                                            </a>{' '}
                                            based on industry best practices.
                                        </Trans>
                                    </Text>
                                    <Banner
                                        title={t`Your policies are already mapped based on Drata's current defaults.`}
                                    />
                                </Stack>
                            )}
                            {hasDefaultPolicies && (
                                <Stack direction="column" gap="4x">
                                    <Text as="div">{t`Here are the policies that would be added and removed if you choose to apply Drata's default. Only active policies will be listed on the control.`}</Text>
                                    <Banner
                                        title={t`Any custom policies you have won't be affected.`}
                                    />
                                    <Stack gap="6x" direction="column">
                                        <Tabs
                                            overflowLeftLabel={t`Scroll tabs to the left`}
                                            overflowRightLabel={t`Scroll tabs to the right`}
                                            hasPadding={false}
                                            tabs={tabs}
                                            defaultTabId={
                                                isEmpty(policiesToAdd)
                                                    ? 'default-remove-tab'
                                                    : 'default-add-tab'
                                            }
                                        />
                                    </Stack>
                                </Stack>
                            )}
                        </>
                    )}
                </Modal.Body>
                {!isLoading && (
                    <Modal.Footer
                        rightActionStack={
                            hasDefaultPolicies
                                ? [
                                      {
                                          level: 'secondary',
                                          label: t`Keep current policies`,
                                          onClick: () => {
                                              modalController.closeModal(
                                                  'policy-compare-to-defaults-modal',
                                              );
                                          },
                                      },
                                      {
                                          level: 'primary',
                                          label: t`Apply defaults`,
                                          isLoading: isSaving,
                                          disabled: isSaving,
                                          onClick: () => {
                                              sharedResetPoliciesModalModel.confirmResetPolicies();
                                          },
                                      },
                                  ]
                                : [
                                      {
                                          level: 'secondary',
                                          label: t`Close`,
                                          onClick: () => {
                                              modalController.closeModal(
                                                  'policy-compare-to-defaults-modal',
                                              );
                                          },
                                      },
                                  ]
                        }
                    />
                )}
            </>
        );
    },
);
