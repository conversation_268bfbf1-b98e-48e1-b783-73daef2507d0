import { isEmpty } from 'lodash-es';
import { useCallback } from 'react';
import { RequestChangesCompletedModal } from '@components/access-review';
import { sharedAccessReviewController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { sharedDatatable } from '@models/access-review';
import { CompletedReviewPeriodContainer } from './components';

export const AccessReviewCompletedReviewsView = observer(
    (): React.JSX.Element => {
        const handleEditReview = useCallback(
            (periodId: number, startDate: string, endDate: string) => {
                const reviewPeriod = {
                    id: periodId,
                    startDate,
                    endDate,
                    completedAt: new Date().toISOString(), // These are completed reviews
                };

                modalController.openModal({
                    id: `edit-review-period-${periodId}`,
                    content: () => (
                        <RequestChangesCompletedModal
                            periodId={periodId}
                            reviewPeriod={reviewPeriod}
                            modalId={`edit-review-period-${periodId}`}
                            data-id="edit-review-period-modal"
                        />
                    ),
                    centered: true,
                    disableClickOutsideToClose: true,
                    size: 'lg',
                });
            },
            [],
        );

        const { accessReviewCompletedList, isLoadingCompleted } =
            sharedAccessReviewController;

        const { datatableColumns } = sharedDatatable;

        if (isLoadingCompleted) {
            return (
                <Stack
                    direction="column"
                    align="center"
                    gap="6x"
                    p="8x"
                    data-testid="AccessReviewCompletedReviewsView"
                    data-id="5DYP_Z0x"
                >
                    <Loader label={t`Loading completed reviews`} />
                </Stack>
            );
        }

        if (isEmpty(accessReviewCompletedList)) {
            return (
                <EmptyState
                    title={t`No completed reviews found`}
                    description={t`No completed reviews found`}
                />
            );
        }

        return (
            <Stack
                direction="column"
                gap="6x"
                p="8x"
                data-testid="AccessReviewCompletedReviewsView"
                data-id="5DYP_Z0x"
            >
                {accessReviewCompletedList.map(
                    ({ applications, startDate, endDate, id }) => {
                        const startDateFormatted = startDate.split('T')[0];
                        const endDateFormatted = endDate.split('T')[0];

                        const startDateLabel = formatDate(
                            'sentence',
                            startDateFormatted,
                        ).split('T')[0];
                        const endDateLabel = formatDate(
                            'sentence',
                            endDateFormatted,
                        ).split('T')[0];

                        return (
                            <CompletedReviewPeriodContainer
                                key={id}
                                applications={applications}
                                startDate={startDateLabel}
                                endDate={endDateLabel}
                                id={id}
                                isLoading={isLoadingCompleted}
                                datatableColumns={datatableColumns}
                                data-id="P6jbFCbM"
                                onEditReview={handleEditReview}
                            />
                        );
                    },
                )}
            </Stack>
        );
    },
);
