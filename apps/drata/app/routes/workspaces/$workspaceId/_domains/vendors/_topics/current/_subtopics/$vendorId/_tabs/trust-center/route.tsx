import type { ClientLoader } from '@app/types';
import { sharedVendorTrustCenterController } from '@controllers/vendors';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Trust Center' },
];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorTrustCenterController.loadOverview(Number(vendorId));
        sharedVendorTrustCenterController.loadCertifications(Number(vendorId));

        return null;
    },
);

const VendorsCurrentTrustCenter = (): React.JSX.Element => {
    return (
        <section data-testid="VendorsCurrentTrustCenter" data-id="Bwg9WWyA">
            <Outlet />
        </section>
    );
};

export default VendorsCurrentTrustCenter;
