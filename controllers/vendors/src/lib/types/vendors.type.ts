import type { vendorsControllerListVendorsOptions } from '@globals/api-sdk/queries';

export interface FilterValue {
    value: string;
    label: string;
}

export type VendorsQuery = NonNullable<
    Parameters<typeof vendorsControllerListVendorsOptions>[0]
>['query'];

export type VendorType = NonNullable<VendorsQuery>['type'];
export type SecurityReviewStatusType =
    NonNullable<VendorsQuery>['securityReviewStatus'];
export type ReviewStatusType = NonNullable<VendorsQuery>['reviewStatus'];
export type VendorStatusType = NonNullable<VendorsQuery>['status'];
export type ImpactLevelType = NonNullable<VendorsQuery>['impactLevel'];
export type VendorRiskType = NonNullable<VendorsQuery>['risk'];
export type DeadlineStatusType =
    NonNullable<VendorsQuery>['nextReviewDeadlineStatus'];
export type BusinessUnitType = NonNullable<VendorsQuery>['category'];
export type SubProcessorType = NonNullable<VendorsQuery>['isSubProcessor'];
export type ScheduledQuestionnairesType =
    NonNullable<VendorsQuery>['scheduledQuestionnaireStatus'];

export interface QuestionnaireData {
    isCompleted: boolean;
    isManualUpload: boolean;
    dateSent: string;
}
