import { isEmpty } from 'lodash-es';
import type { GlobalFilterState } from '@cosmos/components/datatable';
import {
    VENDORS_CURRENT_FILTER_BUSINESS_UNIT_ID,
    VENDORS_CURRENT_FILTER_IMPACT_LEVELS_ID,
    VENDORS_CURRENT_FILTER_NEXT_REVIEW_DEADLINE_ID,
    VENDORS_CURRENT_FILTER_RISK_ID,
    VENDORS_CURRENT_FILTER_SCHEDULED_QUESTIONNAIRES_ID,
    VENDORS_CURRENT_FILTER_SECURITY_REVIEW_STATUS_ID,
    VENDORS_CURRENT_FILTER_STATUS_ID,
    VENDORS_CURRENT_FILTER_SUBPROCESSORS_ID,
    VENDORS_CURRENT_FILTER_TYPE_ID,
} from '../constants/vendors-current-controller.constants';
import {
    VENDORS_PROSPECTIVE_FILTER_IMPACT_LEVELS_ID,
    VENDORS_PROSPECTIVE_FILTER_SECURITY_REVIEW_STATUS_ID,
} from '../constants/vendors-prospective-controller.constants';
import type {
    BusinessUnitType,
    DeadlineStatusType,
    FilterValue,
    ImpactLevelType,
    ReviewStatusType,
    ScheduledQuestionnairesType,
    SecurityReviewStatusType,
    SubProcessorType,
    VendorRiskType,
    VendorStatusType,
    VendorType,
} from '../types/vendors.type';

interface FormattedVendorsCurrentFilters {
    type: VendorType | undefined;
    securityReviewStatus: SecurityReviewStatusType | undefined;
    status: VendorStatusType | undefined;
    impactLevel: ImpactLevelType | undefined;
    risk: VendorRiskType | undefined;
    nextReviewDeadlineStatus: DeadlineStatusType | undefined;
    category: BusinessUnitType | undefined;
    isSubProcessor: SubProcessorType | undefined;
    scheduledQuestionnaireStatus: ScheduledQuestionnairesType | undefined;
}

interface FormattedVendorsProspectiveFilters {
    impactLevel: ImpactLevelType | undefined;
    reviewStatus: ReviewStatusType | undefined;
}

export const convertStatusToVendorStatusType = (
    status: string | null,
): VendorStatusType | null => {
    switch (status) {
        case 'PROSPECTIVE': {
            return 'PROSPECTIVE';
        }
        case 'ACTIVE': {
            return 'ACTIVE';
        }
        case 'ARCHIVED': {
            return 'ARCHIVED';
        }
        case 'APPROVED': {
            return 'APPROVED';
        }
        case 'REJECTED': {
            return 'REJECTED';
        }
        case 'FLAGGED': {
            return 'FLAGGED';
        }
        case 'ON_HOLD': {
            return 'ON_HOLD';
        }
        case 'OFFBOARDED': {
            return 'OFFBOARDED';
        }
        case 'UNDER_REVIEW': {
            return 'UNDER_REVIEW';
        }
        case 'NONE': {
            return 'NONE';
        }
        default: {
            return undefined;
        }
    }
};

const formatFilter = <T>(
    filters: GlobalFilterState['filters'],
    key: string,
    isDirectValue = false,
): T | undefined => {
    const filterData = filters[key];

    if (!(key in filters) || isEmpty(filterData.value)) {
        return undefined;
    }

    if (isDirectValue) {
        return filterData.value as T;
    }

    const filter = filterData.value as FilterValue;

    return filter.value as T;
};

const formatCurrentTypeFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['type'] =>
    formatFilter<VendorType>(filters, VENDORS_CURRENT_FILTER_TYPE_ID);

const formatCurrentSecurityReviewStatusFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['securityReviewStatus'] =>
    formatFilter<SecurityReviewStatusType>(
        filters,
        VENDORS_CURRENT_FILTER_SECURITY_REVIEW_STATUS_ID,
    );

const formatCurrentStatusFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['status'] =>
    formatFilter<VendorStatusType>(filters, VENDORS_CURRENT_FILTER_STATUS_ID);

const formatCurrentImpactLevelFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['impactLevel'] =>
    formatFilter<ImpactLevelType>(
        filters,
        VENDORS_CURRENT_FILTER_IMPACT_LEVELS_ID,
    );

const formatCurrentRiskFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['risk'] =>
    formatFilter<VendorRiskType>(filters, VENDORS_CURRENT_FILTER_RISK_ID);

const formatCurrentNextReviewDeadlineFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['nextReviewDeadlineStatus'] =>
    formatFilter<DeadlineStatusType>(
        filters,
        VENDORS_CURRENT_FILTER_NEXT_REVIEW_DEADLINE_ID,
    );

const formatCurrentCategoryFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['category'] =>
    formatFilter<BusinessUnitType>(
        filters,
        VENDORS_CURRENT_FILTER_BUSINESS_UNIT_ID,
    );

const formatCurrentSubProcessorFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['isSubProcessor'] =>
    formatFilter<SubProcessorType>(
        filters,
        VENDORS_CURRENT_FILTER_SUBPROCESSORS_ID,
        true,
    );

const formatCurrentScheduledQuestionnaireFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters['scheduledQuestionnaireStatus'] =>
    formatFilter<ScheduledQuestionnairesType>(
        filters,
        VENDORS_CURRENT_FILTER_SCHEDULED_QUESTIONNAIRES_ID,
        true,
    );

const formatProspectiveImpactLevelFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsProspectiveFilters['impactLevel'] =>
    formatFilter<ImpactLevelType>(
        filters,
        VENDORS_PROSPECTIVE_FILTER_IMPACT_LEVELS_ID,
    );

const formatProspectiveSecurityReviewStatusFilter = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsProspectiveFilters['reviewStatus'] =>
    formatFilter<ReviewStatusType>(
        filters,
        VENDORS_PROSPECTIVE_FILTER_SECURITY_REVIEW_STATUS_ID,
    );

export const formatVendorsCurrentFilters = (
    filters: GlobalFilterState['filters'],
): FormattedVendorsCurrentFilters => {
    return {
        type: formatCurrentTypeFilter(filters),
        securityReviewStatus: formatCurrentSecurityReviewStatusFilter(filters),
        status: formatCurrentStatusFilter(filters),
        impactLevel: formatCurrentImpactLevelFilter(filters),
        risk: formatCurrentRiskFilter(filters),
        nextReviewDeadlineStatus:
            formatCurrentNextReviewDeadlineFilter(filters),
        category: formatCurrentCategoryFilter(filters),
        isSubProcessor: formatCurrentSubProcessorFilter(filters),
        scheduledQuestionnaireStatus:
            formatCurrentScheduledQuestionnaireFilter(filters),
    };
};

export const formatVendorsProspectiveFilters = (
    filters: GlobalFilterState['filters'] = {},
): FormattedVendorsProspectiveFilters => {
    return {
        impactLevel: formatProspectiveImpactLevelFilter(filters),
        reviewStatus: formatProspectiveSecurityReviewStatusFilter(filters),
    };
};
