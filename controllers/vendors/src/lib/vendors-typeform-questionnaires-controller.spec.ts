import util from 'node:util';
import { beforeAll, describe, expect, test } from 'vitest';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { client } from '@globals/api-sdk';
import { autorun, toJS, when } from '@globals/mobx';
import { sharedVendorsTypeformQuestionnairesController } from './vendors-typeform-questionnaires-controller';

// eslint-disable-next-line vitest/no-disabled-tests -- vendorsQuestionnairesController
describe.skip('vendorsQuestionnairesController', () => {
    beforeAll(async () => {
        const res = await client.get<{ accessToken: string }>({
            url: '/auth/temp/site-admin',
            query: { email: '<EMAIL>' },
        });

        if (res.data?.accessToken) {
            const authToken = res.data.accessToken;

            client.setConfig({
                headers: {
                    Authorization: `Bearer ${authToken}`,
                },
            });
        } else {
            throw new Error('DID NOT RECEIVE JWT TOKEN');
        }
    });

    test('should load vendors questionnaires', async () => {
        sharedVendorsTypeformQuestionnairesController.loadQuestionnaires({
            pagination: { page: 1, pageSize: 10 },
            globalFilter: { search: '' },
        } as FetchDataResponseParams);

        autorun(() => {
            console.debug(
                'resolve vendorsQuestionnaires isLoading',
                sharedVendorsTypeformQuestionnairesController.isLoading,
            );
            console.debug(
                'resolve vendorsQuestionnaires queryError',
                sharedVendorsTypeformQuestionnairesController
                    .allVendorsQuestionnairesQuery.queryError,
            );
            console.debug(
                'resolve vendorsQuestionnaires data',
                util.inspect(
                    sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnaires,
                    false,
                    1,
                    true,
                ),
            );
        });

        await when(
            () => !sharedVendorsTypeformQuestionnairesController.isLoading,
        );

        expect(
            toJS(
                sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnaires,
            ),
        ).instanceOf(Array);
    });
});
