import {
    vendorsControllerGetVendorTrustCenterCertificationsOptions,
    vendorsControllerGetVendorTrustCenterOverviewOptions,
} from '@globals/api-sdk/queries';
import type {
    VendorTrustCenterCertificationResponseDto,
    VendorTrustCenterOverviewResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class VendorTrustCenterController {
    constructor() {
        makeAutoObservable(this);
    }

    #vendorTrustCenterOverviewQuery = new ObservedQuery(
        vendorsControllerGetVendorTrustCenterOverviewOptions,
    );

    #vendorTrustCenterCertificationsQuery = new ObservedQuery(
        vendorsControllerGetVendorTrustCenterCertificationsOptions,
    );

    get overview(): VendorTrustCenterOverviewResponseDto['overview'] | null {
        return this.#vendorTrustCenterOverviewQuery.data?.overview ?? null;
    }

    get certifications(): VendorTrustCenterCertificationResponseDto[] {
        return (
            this.#vendorTrustCenterCertificationsQuery.data?.compliances ?? []
        );
    }

    get isOverviewLoading(): boolean {
        return this.#vendorTrustCenterOverviewQuery.isLoading;
    }

    get isCertificationsLoading(): boolean {
        return this.#vendorTrustCenterCertificationsQuery.isLoading;
    }

    loadOverview = (vendorId: number) => {
        this.#vendorTrustCenterOverviewQuery.load({
            path: { id: vendorId },
        });
    };

    loadCertifications = (vendorId: number) => {
        this.#vendorTrustCenterCertificationsQuery.load({
            path: { id: vendorId },
        });
    };
}

export const sharedVendorTrustCenterController =
    new VendorTrustCenterController();
