import { isEmpty, isObject, isString } from 'lodash-es';
import type {
    VendorQuestionnaireCategoryType,
    VendorQuestionnaireRiskType,
    VendorQuestionnaireStatus,
} from '@components/vendor-questionnaires';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { questionnaireVendorSecurityTypeformControllerGetVendorsQuestionnairesOptions } from '@globals/api-sdk/queries';
import type { QuestionnaireVendorResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

/**
 * Helper function to safely extract filter value.
 */
const getFilterValue = (filter: unknown): string | undefined => {
    if (filter && isObject(filter) && 'value' in filter) {
        const filterObj = filter as { value: unknown };

        if (
            filterObj.value &&
            isObject(filterObj.value) &&
            'value' in filterObj.value
        ) {
            const nestedValue = (filterObj.value as { value: unknown }).value;

            return isString(nestedValue) ? nestedValue : undefined;
        }
    }

    return undefined;
};

class VendorsTypeformQuestionnairesController {
    #lastSearchQuery = '';
    hasFilters = false;

    constructor() {
        makeAutoObservable(this);
    }

    allVendorsQuestionnairesQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerGetVendorsQuestionnairesOptions,
    );

    get allVendorsQuestionnaires(): QuestionnaireVendorResponseDto[] {
        return this.allVendorsQuestionnairesQuery.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.allVendorsQuestionnairesQuery.isLoading;
    }

    get total(): number {
        return this.allVendorsQuestionnairesQuery.data?.total ?? 0;
    }

    get hasNextPage(): boolean {
        const { data } = this.allVendorsQuestionnairesQuery;

        if (!data) {
            return false;
        }

        return data.page < data.total;
    }

    get formattedQuestionnaireOptions(): {
        id: string;
        label: string;
        value: string;
    }[] {
        return this.allVendorsQuestionnaires.map((questionnaire) => ({
            id: questionnaire.id.toString(),
            label: questionnaire.title,
            value: questionnaire.id.toString(),
        }));
    }

    loadQuestionnaires = (params: FetchDataResponseParams): void => {
        const { pagination, globalFilter } = params;
        const { search = '', filters = {} } = globalFilter;
        const { pageSize, page = 1 } = pagination;

        this.#lastSearchQuery = search;
        this.hasFilters = !isEmpty(search.trim()) || !isEmpty(filters);

        // Extract filter values
        const statusFilter =
            filters['vendors-questionnaires-table-filter-status'];
        const categoryFilter =
            filters['vendors-questionnaires-table-filter-category'];
        const riskFilter = filters['vendors-questionnaires-table-filter-risk'];

        const queryParams = {
            q: search || undefined,
            prioritizeCategory: false,
            page,
            limit: pageSize,
            status: getFilterValue(statusFilter) as
                | VendorQuestionnaireStatus
                | undefined,
            categories: getFilterValue(categoryFilter) as
                | VendorQuestionnaireCategoryType
                | undefined,
            riskLevels: getFilterValue(riskFilter) as
                | VendorQuestionnaireRiskType
                | undefined,
        };

        this.allVendorsQuestionnairesQuery.load({
            query: queryParams,
        });
    };

    handleFetchOptions = (params: {
        search?: string;
        increasePage?: boolean;
        filters?: Record<string, unknown>;
    }) => {
        const { search, increasePage, filters = {} } = params;

        if (increasePage) {
            this.loadNextPage({ search, filters });
        } else {
            this.loadQuestionnaires({
                pagination: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
                globalFilter: { search: search || '', filters },
            } as FetchDataResponseParams);
        }
    };

    loadNextPage = ({
        search,
        filters = {},
    }: {
        search?: string;
        filters?: Record<string, unknown>;
    }): void => {
        if (search !== this.#lastSearchQuery) {
            this.loadQuestionnaires({
                pagination: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
                globalFilter: { search: search || '', filters },
            } as FetchDataResponseParams);

            return;
        }

        if (this.hasNextPage) {
            const currentPage =
                this.allVendorsQuestionnairesQuery.data?.page ?? 1;

            this.loadQuestionnaires({
                pagination: {
                    page: currentPage,
                    pageSize: DEFAULT_PAGE_SIZE,
                },
                globalFilter: { search: this.#lastSearchQuery, filters },
            } as FetchDataResponseParams);
        }
    };
}

export const sharedVendorsTypeformQuestionnairesController =
    new VendorsTypeformQuestionnairesController();
