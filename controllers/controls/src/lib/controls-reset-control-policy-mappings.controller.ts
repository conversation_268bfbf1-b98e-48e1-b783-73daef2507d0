import { grcControllerResetControlPolicyMappingsMutation } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

class ControlsResetControlPolicyMappingsController {
    constructor() {
        makeAutoObservable(this);
    }

    resetControlPolicyMappingsMutation = new ObservedMutation(
        grcControllerResetControlPolicyMappingsMutation,
    );

    get hasError(): boolean {
        return this.resetControlPolicyMappingsMutation.hasError;
    }

    get isPending(): boolean {
        return this.resetControlPolicyMappingsMutation.isPending;
    }

    resetControlPolicyMappings = (controlId: number): void => {
        this.resetControlPolicyMappingsMutation.mutate({
            path: { controlId },
        });
    };
}

export const sharedControlsResetControlPolicyMappingsController =
    new ControlsResetControlPolicyMappingsController();
