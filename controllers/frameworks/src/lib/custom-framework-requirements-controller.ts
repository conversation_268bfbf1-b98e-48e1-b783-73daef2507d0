import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { FilterStateValue } from '@cosmos/components/filter-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    customFrameworksControllerDeleteCustomRequirementsMutation,
    customFrameworksControllerGetCustomCategoriesOptions,
    customFrameworksControllerUpdateCustomRequirementCategoriesMutation,
    grcControllerGetAllRequirementsOptions,
    grcControllerUpdateRequirementsScopeMutation,
} from '@globals/api-sdk/queries';
import type {
    GetCustomCategoriesResponseDto,
    RequirementListResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedFrameworkDetailsController } from './framework-details-controller';
import type {
    BaseRequirementProps,
    UpdateCustomFrameworkRequirementsScopeProps,
    UpdateCustomRequirementCategoriesProps,
} from './types/framework-props.type';

type Query = NonNullable<
    Parameters<typeof grcControllerGetAllRequirementsOptions>[0]['query']
>;

class CustomFrameworkRequirementsController {
    customCategory: FilterStateValue;
    isReady: FilterStateValue;
    isInScope: FilterStateValue;
    hasEverHadData = false;

    constructor() {
        makeAutoObservable(this);
    }

    customFrameworkRequirementsQuery = new ObservedQuery(
        grcControllerGetAllRequirementsOptions,
    );

    #deleteCustomRequirementMutation = new ObservedMutation(
        customFrameworksControllerDeleteCustomRequirementsMutation,
    );

    #updateRequirementsScopeMutation = new ObservedMutation(
        grcControllerUpdateRequirementsScopeMutation,
    );

    #getCustomCategoriesQuery = new ObservedQuery(
        customFrameworksControllerGetCustomCategoriesOptions,
    );

    #updateCustomRequirementCategoriesMutation = new ObservedMutation(
        customFrameworksControllerUpdateCustomRequirementCategoriesMutation,
    );

    get requirements(): RequirementListResponseDto[] {
        return this.customFrameworkRequirementsQuery.data?.data ?? [];
    }

    get customFrameworkRequirementsTotal(): number {
        return this.customFrameworkRequirementsQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return (
            this.customFrameworkRequirementsQuery.isLoading ||
            this.customFrameworkRequirementsQuery.isReady
        );
    }

    get shouldShowFirstTimeEmpty(): boolean {
        return (
            !this.hasEverHadData && this.customFrameworkRequirementsTotal === 0
        );
    }

    markDataLoaded(): void {
        // Don't update flag if:
        // 1. Already marked as having seen data, OR
        // 2. Current API response has zero requirements
        if (
            this.hasEverHadData ||
            this.customFrameworkRequirementsTotal === 0
        ) {
            return;
        }

        // - hasEverHadData = false → "No data" empty state (first time)
        // - hasEverHadData = true → "No results" empty state (filtered/searched)
        this.hasEverHadData = true;
    }

    get isDeleting(): boolean {
        return this.#deleteCustomRequirementMutation.isPending;
    }

    get isUpdatingScope(): boolean {
        return this.#updateRequirementsScopeMutation.isPending;
    }

    get customCategories(): GetCustomCategoriesResponseDto | null {
        return this.#getCustomCategoriesQuery.data;
    }

    get isLoadingCategories(): boolean {
        return this.#getCustomCategoriesQuery.isLoading;
    }

    get isUpdatingCategory(): boolean {
        return this.#updateCustomRequirementCategoriesMutation.isPending;
    }

    get categoryOptions(): ListBoxItemData[] {
        const categories = this.customCategories;

        if (!categories?.data) {
            return [];
        }

        return categories.data.map((category, index) => ({
            id: `category-${index}`,
            label: category.label,
            value: category.label,
        }));
    }

    loadCustomFrameworkRequirements = (
        params: FetchDataResponseParams,
    ): void => {
        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace?.id),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                if (!workspaceId) {
                    return;
                }

                const { frameworkDetails } = sharedFrameworkDetailsController;
                const frameworkId = frameworkDetails?.id;
                const frameworkSlug = frameworkDetails?.slug;

                if (!frameworkId || !frameworkSlug) {
                    return;
                }

                const { pagination, globalFilter } = params;
                const { page, pageSize } = pagination;
                const { search, filters } = globalFilter;
                const { isReady, customCategory, isInScope } = filters;

                const query: Query = {
                    page,
                    q: search ?? '',
                    limit: pageSize,
                    frameworkId,
                    frameworkSlug,
                };

                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- TS is wrong, customCategory not guaranteed to exist
                this.customCategory = customCategory?.value;
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- TS is wrong, isReady not guaranteed to exist
                this.isReady = isReady?.value;
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- TS is wrong, isInScope not guaranteed to exist
                this.isInScope = isInScope?.value;

                if (this.isReady) {
                    query.isReady = this.isReady === 'true';
                }

                if (this.customCategory) {
                    query.customCategory = customCategory.value as string;
                }

                if (this.isInScope) {
                    query.isInScope = isInScope.value === 'true';
                }

                this.customFrameworkRequirementsQuery.load({
                    path: { xProductId: workspaceId },
                    query,
                });
            },
        );
    };

    deleteCustomRequirements = ({
        requirementIds,
        onSuccess,
    }: BaseRequirementProps): void => {
        if (
            !isEmpty(requirementIds) ||
            this.#deleteCustomRequirementMutation.isPending
        ) {
            return;
        }

        this.#deleteCustomRequirementMutation.mutate({
            path: { requirementIdList: requirementIds },
        });

        when(
            () => !this.#deleteCustomRequirementMutation.isPending,
            () => {
                if (this.#deleteCustomRequirementMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'delete-custom-requirement-error',
                        props: {
                            title: t`Failed to delete requirement`,
                            description: t`An error occurred while deleting the requirement. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    if (onSuccess) {
                        onSuccess();
                    }
                    snackbarController.addSnackbar({
                        id: 'delete-custom-requirement-success',
                        props: {
                            title: t`Requirement deleted successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    // Refresh the requirements list
                    this.customFrameworkRequirementsQuery.invalidate();
                }
            },
        );
    };

    updateRequirementsScope = ({
        requirementIds,
        isInScope,
        rationale,
        onSuccess,
    }: UpdateCustomFrameworkRequirementsScopeProps): void => {
        if (
            isEmpty(requirementIds) ||
            this.#updateRequirementsScopeMutation.isPending
        ) {
            return;
        }

        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        this.#updateRequirementsScopeMutation.mutate({
            path: { xProductId: workspaceId },
            body: {
                requirementIds,
                isInScope,
                rationale,
            },
        });

        when(
            () => !this.#updateRequirementsScopeMutation.isPending,
            () => {
                if (this.#updateRequirementsScopeMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'update-requirements-scope-error',
                        props: {
                            title: t`Failed to update requirement scope`,
                            description: t`An error occurred while updating the requirement scope. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    const scopeText = isInScope ? t`in scope` : t`out of scope`;

                    if (onSuccess) {
                        onSuccess();
                    }
                    snackbarController.addSnackbar({
                        id: 'update-requirements-scope-success',
                        props: {
                            title: t`Requirement marked ${scopeText} successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    // Refresh the requirements list
                    this.customFrameworkRequirementsQuery.invalidate();
                }
            },
        );
    };

    loadCustomCategories(frameworkId: number, searchQuery?: string): void {
        this.#getCustomCategoriesQuery.load({
            path: { frameworkId },
            query: {
                q: searchQuery ?? '',
                page: 1,
            },
        });
    }

    updateCustomRequirementCategories = ({
        frameworkId,
        requirementIds,
        category,
        onSuccess,
    }: UpdateCustomRequirementCategoriesProps): void => {
        if (
            isEmpty(requirementIds) ||
            this.#updateCustomRequirementCategoriesMutation.isPending
        ) {
            return;
        }

        this.#updateCustomRequirementCategoriesMutation.mutate({
            body: {
                frameworkId,
                requirementIds,
                category,
            },
        });

        when(
            () => !this.#updateCustomRequirementCategoriesMutation.isPending,
            () => {
                if (this.#updateCustomRequirementCategoriesMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'update-requirement-category-error',
                        props: {
                            title: t`Failed to update requirement category`,
                            description: t`An error occurred while updating the requirement category. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    if (onSuccess) {
                        onSuccess();
                    }
                    snackbarController.addSnackbar({
                        id: 'update-requirement-category-success',
                        props: {
                            title: t`Requirement category updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    // Refresh the requirements list
                    this.customFrameworkRequirementsQuery.invalidate();
                }
            },
        );
    };
}

export const sharedCustomFrameworkRequirementsController =
    new CustomFrameworkRequirementsController();

// Auto-update data flag when requirements count changes (for empty state logic)
reaction(
    () =>
        sharedCustomFrameworkRequirementsController.customFrameworkRequirementsTotal,
    () => {
        sharedCustomFrameworkRequirementsController.markDataLoaded();
    },
);
