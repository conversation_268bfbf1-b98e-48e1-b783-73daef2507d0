import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    accessReviewPeriodControllerGetAccessReviewApplicationsWithWarningsOptions,
    accessReviewPeriodControllerListAccessReviewPeriodsOptions,
} from '@globals/api-sdk/queries';
import type {
    AccessApplicationSummaryResponseDto,
    AccessReviewPeriodResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class AccessReviewController {
    resetPagination(): void {
        this.currentPage = DEFAULT_PAGE;
        this.pageSize = DEFAULT_PAGE_SIZE;
        this.hasLoadedData = false;
    }
    /**
     * Backend is NOT respecting pagination parameters, implementing frontend pagination workaround.
     */
    currentPage = DEFAULT_PAGE;
    pageSize = DEFAULT_PAGE_SIZE;
    hasLoadedData = false;

    constructor() {
        makeAutoObservable(this);
    }

    accessReview = new ObservedQuery(
        accessReviewPeriodControllerGetAccessReviewApplicationsWithWarningsOptions,
    );

    accessReviewComplete = new ObservedQuery(
        accessReviewPeriodControllerListAccessReviewPeriodsOptions,
    );

    get accessReviewList(): AccessApplicationSummaryResponseDto[] {
        const allData = this.accessReview.data?.data ?? [];

        // Frontend pagination since backend doesn't support it
        // Ensure we have valid numbers to prevent NaN calculations
        const currentPage =
            Number.isInteger(this.currentPage) && this.currentPage > 0
                ? this.currentPage
                : DEFAULT_PAGE;
        const pageSize =
            Number.isInteger(this.pageSize) && this.pageSize > 0
                ? this.pageSize
                : DEFAULT_PAGE_SIZE;

        const startIndex = (currentPage - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        return allData.slice(startIndex, endIndex);
    }

    get accessReviewListTotal(): number {
        // Return total count of all data for proper pagination controls
        return this.accessReview.data?.data.length ?? 0;
    }

    get isLoading(): boolean {
        return this.accessReview.isLoading;
    }

    loadAccessReview = (params: FetchDataResponseParams): void => {
        // Update pagination state for frontend pagination
        this.currentPage = params.pagination.page ?? DEFAULT_PAGE;
        this.pageSize = params.pagination.pageSize;

        // Only load data from backend once since backend doesn't respect pagination
        if (this.hasLoadedData) {
            return;
        }

        // Load all data since backend doesn't respect pagination
        this.accessReview.load({
            query: {
                // Remove pagination parameters since backend ignores them
            },
        });
        this.hasLoadedData = true;
    };

    get accessReviewCompletedList(): AccessReviewPeriodResponseDto[] {
        return this.accessReviewComplete.data?.data ?? [];
    }

    get isLoadingCompleted(): boolean {
        return this.accessReviewComplete.isLoading;
    }
}

export const sharedAccessReviewController = new AccessReviewController();
