import { isEmpty, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    accessReviewApplicationControllerDownloadPackageOptions,
    accessReviewUserControllerGetApplicationUsersReviewersOptions,
} from '@globals/api-sdk/queries';
import type {
    AccessReviewApplicationDetailsResponseDto,
    AccessReviewPeriodApplicationEvidenceResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { providers } from '@globals/providers';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import { determineModalToShow } from '@views/access-review-application-period-personnel';
import { sharedAccessReviewPeriodApplicationController } from './access-review-period-application-controller';

class AccessReviewApplicationDetailsController {
    UNIQUE_REVIEWER = 1;

    constructor() {
        makeAutoObservable(this);
    }

    applicationUsersReviewers = new ObservedQuery(
        accessReviewUserControllerGetApplicationUsersReviewersOptions,
    );

    downloadEvidence = new ObservedQuery(
        accessReviewApplicationControllerDownloadPackageOptions,
    );

    get applicationDetails(): AccessReviewApplicationDetailsResponseDto | null {
        return sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplicationDetails;
    }

    get isLoading(): boolean {
        return sharedAccessReviewPeriodApplicationController.isLoading;
    }

    get applicationReviewers(): UserResponseDto[] {
        return this.applicationDetails?.application.reviewers ?? [];
    }

    get usersReviewers(): UserResponseDto[] | undefined {
        return this.applicationUsersReviewers.data?.data;
    }

    get isUsersReviewersLoading(): boolean {
        return this.applicationUsersReviewers.isLoading;
    }

    get displayReviewersBanner(): boolean {
        return determineModalToShow(
            this.usersReviewers ?? [],
            this.applicationReviewers,
        );
    }

    get showLinkedEvidence(): boolean {
        return (
            this.applicationDetails?.application.hasPendingEvidence ||
            !isEmpty(this.applicationDetails?.application.evidences)
        );
    }

    get hasPendingEvidence(): boolean {
        return this.applicationDetails?.application.hasPendingEvidence ?? false;
    }

    get isApplicationDisabled(): boolean {
        const isDeleted: boolean = this.applicationDetails?.application
            .deletedAt
            ? true
            : this.applicationDetails?.application.status === 'DELETED';

        const isCompleted =
            this.applicationDetails?.application.status === 'COMPLETED';

        return isDeleted || isCompleted;
    }

    get applicationEvidences(): AccessReviewPeriodApplicationEvidenceResponseDto[] {
        return this.applicationDetails?.application.evidences ?? [];
    }

    get logo(): string {
        const providerLogo =
            providers[
                this.applicationDetails?.application
                    .clientType as keyof typeof providers
            ];

        if (this.applicationDetails?.application.logo) {
            return this.applicationDetails.application.logo;
        }

        if (
            this.applicationDetails?.application.source ===
                'PARTNER_CONNECTION' ||
            this.applicationDetails?.application.source === 'DIRECT_CONNECTION'
        ) {
            return providerLogo.logo;
        }

        return providers.CUSTOM.logo;
    }

    refreshUsersReviewers = () => {
        this.applicationUsersReviewers.invalidate();
        sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
    };

    loadReviewers = (usersIdsArr: number[]) => {
        this.applicationUsersReviewers.load({
            path: {
                reviewAppId: this.applicationDetails?.application.id ?? 0,
            },
            query: {
                userIds: usersIdsArr,
            },
        });
    };

    downloadLinkedEvidence = ({
        periodId,
        reviewAppId,
        evidenceId,
    }: {
        periodId?: number;
        reviewAppId?: number;
        evidenceId: number;
    }): void => {
        if (isNil(periodId) || isNil(reviewAppId) || isNil(evidenceId)) {
            throw new Error(
                'Period ID, App ID and Evidence ID params are required',
            );
        }

        this.downloadEvidence.load({
            path: {
                periodId,
                reviewAppId,
                evidenceId,
            },
        });

        when(() => !this.downloadEvidence.isLoading)
            .then(() => {
                const blob = this.downloadEvidence.data;

                if (isEmpty(blob)) {
                    snackbarController.addSnackbar({
                        id: 'unable-download-evidence',
                        props: {
                            title: t`Unable to download evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }
                snackbarController.addSnackbar({
                    id: 'download-evidence-success',
                    props: {
                        title: t`Download evidence success`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                downloadFileFromSignedUrl(blob.signedUrl);
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'unable-download-evidence',
                    props: {
                        title: t`Unable to download evidence`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            });
    };
}

export const activeAccessReviewApplicationDetailsController =
    new AccessReviewApplicationDetailsController();
