import { isNil } from 'lodash-es';
import {
    type AccessReviewStatus,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationSummaryController,
    sharedAccessReviewPeriodApplicationUserController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { snackbarController } from '@controllers/snackbar';
import { accessReviewUserControllerUpdateUserStatusBulkActionMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

class AccessReviewBulkActionStatus {
    constructor() {
        makeAutoObservable(this);
    }

    bulkActions = new ObservedMutation(
        accessReviewUserControllerUpdateUserStatusBulkActionMutation,
    );

    periodId: number | null = null;
    applicationId: number | null = null;

    setPeriodId(id: number) {
        this.periodId = id;
    }

    setApplicationId(id: number) {
        this.applicationId = id;
    }

    updateStatus = (
        userIds: number[],
        status: AccessReviewStatus,
        note?: string,
    ): void => {
        if (isNil(this.periodId) || isNil(this.applicationId)) {
            throw new Error('Period ID and App ID are required');
        }

        this.bulkActions
            .mutateAsync({
                path: {
                    periodId: this.periodId,
                    reviewAppId: this.applicationId,
                },
                body: {
                    updateToStatus: status,
                    applicationUserIds: userIds,
                    note,
                },
            })
            .then(() => {
                sharedAccessReviewPeriodApplicationUsersController.accessReviewPeriodApplicationUsers.invalidate();
                sharedAccessReviewPeriodApplicationUserController.accessReviewPeriodApplicationUser.invalidate();
                sharedAccessReviewPeriodApplicationSummaryController.accessReviewPeriodApplicationSummary.invalidate();
                sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
                snackbarController.addSnackbar({
                    id: 'access-review-bulk-action-success',
                    hasTimeout: true,
                    props: {
                        title: t`Status updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            })
            .catch((error: Error) => {
                snackbarController.addSnackbar({
                    id: 'access-review-bulk-action-error',
                    props: {
                        title: t`Failed to complete bulk action`,
                        description: error.message,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
                throw error;
            });
    };
}

export const sharedAccessReviewBulkActionStatusController =
    new AccessReviewBulkActionStatus();
