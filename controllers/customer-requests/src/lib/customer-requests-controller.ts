import { isEmpty, isUndefined } from 'lodash-es';
import type {
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import { customerRequestControllerGetCustomerRequestListOptions } from '@globals/api-sdk/queries';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { getFilterProps } from '../../../../views/auditor-client-audit-details/src/lib/helpers/get-filter-props';

export type CustomerRequestsListQuery = Required<
    Parameters<typeof customerRequestControllerGetCustomerRequestListOptions>
>[0]['query'];

class CustomerRequestsController {
    currentAppliedFilters: CustomerRequestsListQuery | null = null;
    frameworkId = '';

    constructor() {
        makeAutoObservable(this);
    }

    customerRequestListQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestListOptions,
    );

    get customerRequests(): CustomerRequestListItemResponseDto[] {
        return this.customerRequestListQuery.data?.requests.data ?? [];
    }

    get total(): number {
        return this.customerRequestListQuery.data?.requests.total ?? 0;
    }

    get isLoading(): boolean {
        return this.customerRequestListQuery.isLoading;
    }

    get totalUnreadMessages(): number {
        return this.customerRequestListQuery.data?.totalUnreadMessages ?? 0;
    }

    get filterProps(): FilterProps {
        return getFilterProps(
            this.customerRequestListQuery.data?.totalUnreadMessages ?? 0,
        );
    }

    getCustomerRequestList = (params: FetchDataResponseParams) => {
        const { pagination, globalFilter } = params;
        const { pageSize, page } = pagination;
        const { search, filters } = globalFilter;

        const query: CustomerRequestsListQuery = {
            page,
            limit: pageSize,
            framework: this.frameworkId,
        };

        if (search) {
            query.q = search;
        }

        if (!isEmpty(filters.onlyWithNewMessages.value)) {
            query.onlyWithNewMessages = true;
        }

        if (!isUndefined(filters.status.value)) {
            query.status = filters.status
                .value as CustomerRequestsListQuery['status'];
        }

        if (!isUndefined(filters.isOwned.value)) {
            query.isOwned = filters.isOwned.value === 'true';
        }

        this.currentAppliedFilters = query;

        this.customerRequestListQuery.load({
            query,
        });
    };
}

export const sharedCustomerRequestsController =
    new CustomerRequestsController();
