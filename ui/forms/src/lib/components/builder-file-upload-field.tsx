import { FileUploadField } from '@cosmos/components/file-upload-field';
import { t } from '@globals/i18n/macro';
import { FILE_ERROR_CODE_MESSAGES } from '../constants/file-error-code-messages.constant';
import { useFileUploadUpdateHandler } from '../hooks/use-file-upload-update-handler.hook';
import type { BaseField } from '../types/base-field.type';
import type { LimitedFileUploadFieldProps } from '../types/limited-file-upload-field-props.type';
import type { SharedProps } from '../types/shared-props.type';
import type { UniversalFieldController } from '../types/universal-field-controller.type';
import type { UniversalFormFieldProps } from '../types/universal-form-field-props.type';

export interface BuilderFileUploadFieldProps {
    sharedProps: SharedProps;
    props: UniversalFormFieldProps;
    fieldProps: UniversalFieldController;
    fieldSchema: BaseField<LimitedFileUploadFieldProps>;
}

export const BuilderFileUploadField = ({
    props,
    fieldSchema,
    fieldProps,
    sharedProps,
}: BuilderFileUploadFieldProps): React.JSX.Element => {
    const handleUpdate = useFileUploadUpdateHandler(props.name, fieldProps);

    return (
        // eslint-disable-next-line custom/enforce-data-id -- is passed via props
        <FileUploadField
            {...props}
            {...sharedProps}
            data-testid="BuilderFileUploadField"
            initialFiles={fieldProps.value}
            errorCodeMessages={FILE_ERROR_CODE_MESSAGES}
            removeButtonText={t`Remove file`}
            selectButtonText={t`Upload files`}
            innerLabel={t`Or drop files here`}
            onUpdate={handleUpdate}
            {...fieldSchema}
        />
    );
};
