import { UtilitiesComponent } from '@components/utilities';
import { Stack } from '@cosmos/components/stack';
import { PanelRoot } from '@ui/panel-root';
import styles from './page-aside-ui.module.css';

export const PageAsideUi = (): React.JSX.Element => {
    return (
        <aside
            data-testid="PageAsideUi"
            data-id="UC-7yGNY"
            className={styles['page-aside-ui']}
        >
            <Stack align="stretch" height="100%">
                <PanelRoot />
                <UtilitiesComponent />
            </Stack>
        </aside>
    );
};
