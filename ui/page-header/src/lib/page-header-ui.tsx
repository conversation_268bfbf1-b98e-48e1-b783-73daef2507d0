import { routeController } from '@controllers/route';
import { Box } from '@cosmos/components/box';
import { PageHeader as CosmosPageHeader } from '@cosmos/components/page-header';
import { observer, toJS } from '@globals/mobx';
import { MAX_CENTERED_APPMAIN_WIDTH } from '@ui/layout-constants';

export const PageHeaderUi = observer((): React.JSX.Element => {
    const { pageHeader } = routeController;

    const {
        actionStack,
        backLink,
        banner,
        keyValuePairs,
        pageId,
        slot,
        topAlignSlot,
        title,
        breadcrumbs,
        isLoading,
        isCentered,
    } = pageHeader ?? {};

    return (
        <Box
            width="100%"
            data-id="page-header-ui-stack"
            {...(isCentered ? { maxWidth: MAX_CENTERED_APPMAIN_WIDTH } : {})}
        >
            <CosmosPageHeader
                actionStack={toJS(actionStack)}
                backLink={toJS(backLink)}
                banner={toJS(banner)}
                keyValuePairs={toJS(keyValuePairs)}
                pageId={toJS(pageId) ?? ''}
                slot={toJS(slot)}
                topAlignSlot={toJS(topAlignSlot)}
                title={toJS(title) ?? ''}
                breadcrumbs={toJS(breadcrumbs)}
                data-id="ps1PXMwe"
                isLoading={toJS(isLoading ?? false)}
            />
        </Box>
    );
});
