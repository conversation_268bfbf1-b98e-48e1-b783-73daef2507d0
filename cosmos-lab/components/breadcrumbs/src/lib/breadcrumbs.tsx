import { Button } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { AppLink } from '@ui/app-link';
import type { Breadcrumb } from './types/breadcrumbs.types';

export interface BreadcrumbsProps {
    /**
     * Array of breadcrumb objects.
     */
    breadcrumbs: Breadcrumb[];

    /**
     * Data test id.
     */
    'data-id'?: string;

    /**
     * Optional click handler for breadcrumb segments.
     */
    onClick?: (id: string) => void;
}

/**
 * The Breadcrumbs component helps users understand where they are within a website's structure and move between levels.
 *
 * 🚧 Needs Figma Link.
 */
export const Breadcrumbs = ({
    breadcrumbs,
    'data-id': dataId,
    onClick,
}: BreadcrumbsProps): React.JSX.Element => {
    return (
        <nav aria-label="Breadcrumb" data-testid="Breadcrumbs" data-id={dataId}>
            <Stack direction="row" gap="sm" align="center" py="sm">
                {breadcrumbs.map(({ pathname, label }) => {
                    return (
                        <Stack
                            key={pathname}
                            gap="sm"
                            data-id={`${dataId}-${label}BreadcrumbSegment`}
                            align="center"
                        >
                            {onClick ? (
                                <Button
                                    size="sm"
                                    colorScheme="primary"
                                    level="tertiary"
                                    label={label}
                                    onClick={() => {
                                        onClick(pathname);
                                    }}
                                />
                            ) : (
                                <AppLink
                                    href={pathname}
                                    data-id={dataId}
                                    size="sm"
                                >
                                    {label}
                                </AppLink>
                            )}
                            <Icon name="Slash" size="100" colorScheme="faded" />
                        </Stack>
                    );
                })}
            </Stack>
        </nav>
    );
};
