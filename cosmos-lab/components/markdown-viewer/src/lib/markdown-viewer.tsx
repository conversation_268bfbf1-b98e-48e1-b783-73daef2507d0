import Markdown from 'markdown-to-jsx';
import { Stack } from '@cosmos/components/stack';
import { MARKDOWN_OPTIONS } from './constants/markdown-options.constant';

export interface MarkdownViewerProps {
    children: string;
    'data-id': string;
}

export const MarkdownViewer = ({
    children,
    'data-id': dataId,
}: MarkdownViewerProps): React.JSX.Element => {
    // This is used to be able to have line breaks in the markdown
    const formatted = children.replaceAll('\n', '  \n');

    return (
        <Stack
            direction="column"
            gap="4x"
            data-testid="MarkdownViewer"
            data-id={dataId}
        >
            <Markdown options={MARKDOWN_OPTIONS}>{formatted}</Markdown>
        </Stack>
    );
};
