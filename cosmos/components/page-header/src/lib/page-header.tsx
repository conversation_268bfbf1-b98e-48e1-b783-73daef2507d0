import { isEmpty } from 'lodash-es';
import { isValidElement, type ReactElement, type ReactNode } from 'react';
import { Box } from '@cosmos/components/box';
import {
    KeyValuePair,
    type KeyValuePairProps,
} from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import {
    type Breadcrumb,
    Breadcrumbs,
} from '@cosmos-lab/components/breadcrumbs';
import { PageHeaderActions, PageHeaderBackLink, PageTitle } from './components';
import { PageHeaderSkeleton } from './components/page-header-skeleton.component';
import { DEFAULT_DATA_ID } from './constants';
import { createTestIds, getGridTemplateAreas } from './helpers';
import { useResponsiveLayout } from './hooks';
import { StyledGridAreaDiv, StyledPageHeaderWrapperDiv } from './styles';

/**
 * Helper function to detect if a ReactNode is a Metadata component.
 */
function isMetadataComponent(node: ReactNode): boolean {
    return isValidElement(node) && node.type === Metadata;
}

export interface PageHeaderProps {
    /**
     * An array of PageHeaderActionStackType objects or an ActionStack that will be rendered in the header.
     */
    actionStack?: ReactElement;
    /**
     * An optional banner to be displayed at the bottom of the header.
     */
    banner?: ReactNode;
    /**
     * A "Back" link that sends user to the previous page.
     */
    backLink?: ReactNode;
    /**
     * An array of breadcrumbs to be displayed in the header.
     */
    breadcrumbs?: Breadcrumb[] | null;
    /**
     * Custom skeleton to be displayed when the page header is in a loading state.
     */
    customSkeleton?: ReactElement;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * If `true`, a skeleton will render instead of body.
     */
    isLoading?: boolean;
    /**
     * An array of `KeyValuePairProps` used to render `KeyValuePair`s in the header.
     */
    keyValuePairs?: KeyValuePairProps[] | null;
    /**
     * A unique identifier for this page, used to create IDs for child elements.
     */
    pageId: string;
    /**
     * The content of the Slot.
     */
    slot?: ReactNode;
    /**
     * The title of the page.
     */
    title: string;
    /** If `true`, the slot will be aligned with the top of the title. */
    topAlignSlot?: boolean;
}

/**
 * [AI generated] Provides consistent page titles, navigation elements, and action controls at the top of application pages with support for breadcrumbs and loading states.
 *
 * 🚧 Needs Figma Link.
 */
export const PageHeader = ({
    actionStack = undefined,
    backLink = undefined,
    breadcrumbs = undefined,
    banner = undefined,
    'data-id': dataId = DEFAULT_DATA_ID,
    isLoading = false,
    keyValuePairs = undefined,
    pageId,
    slot,
    title,
    topAlignSlot,
    customSkeleton = undefined,
}: Readonly<PageHeaderProps>): React.JSX.Element => {
    const ACTIONS_ID = `${pageId}-actions`;

    const { layout, resizer } = useResponsiveLayout();

    const gridTemplate = getGridTemplateAreas({
        layout,
        backLink,
        breadcrumbs,
        actionStack,
        keyValuePairs,
        banner,
    });

    const {
        backLinkSlotTestId,
        titleSlotTestId,
        actionsSlotTestId,
        keyValuePairsSlotTestId,
        bannerSlotTestId,
        breadcrumbsSlotTestId,
    } = createTestIds(dataId);

    const backlinksPadding = { px: '3xl', py: 'md' } as const as Record<
        string,
        string
    >;

    if (isLoading) {
        return (
            <Stack direction="column" data-testid="PageHeader" data-id={dataId}>
                {backLink !== undefined && (
                    <Box {...backlinksPadding}>
                        <PageHeaderBackLink
                            backLink={backLink}
                            data-id={backLinkSlotTestId}
                        />
                    </Box>
                )}

                {breadcrumbs && (
                    <Box {...backlinksPadding}>
                        <Breadcrumbs
                            breadcrumbs={breadcrumbs}
                            data-id={breadcrumbsSlotTestId}
                        />
                    </Box>
                )}
                <Stack ref={resizer} p="3xl" width="100%">
                    {customSkeleton ? (
                        <>{customSkeleton}</>
                    ) : (
                        <PageHeaderSkeleton layout={layout} />
                    )}
                </Stack>
            </Stack>
        );
    }

    return (
        <Stack direction="column" data-testid="PageHeader" data-id={dataId}>
            {backLink !== undefined && (
                <Box {...backlinksPadding}>
                    <PageHeaderBackLink
                        backLink={backLink}
                        data-id={backLinkSlotTestId}
                    />
                </Box>
            )}

            {breadcrumbs && (
                <Box {...backlinksPadding}>
                    <Breadcrumbs
                        breadcrumbs={breadcrumbs}
                        data-id={breadcrumbsSlotTestId}
                    />
                </Box>
            )}

            <StyledPageHeaderWrapperDiv
                ref={resizer}
                $gridTemplate={gridTemplate}
                $layout={layout}
                data-testid="PageHeader"
            >
                {/* Note: order of these elements changes depending on page width */}
                <PageTitle
                    title={title}
                    topAlignSlot={topAlignSlot ?? isMetadataComponent(slot)}
                    slot={slot}
                    layout={layout}
                    data-id={titleSlotTestId}
                />
                {actionStack !== undefined && (
                    <PageHeaderActions
                        actionStack={actionStack}
                        id={ACTIONS_ID}
                        layout={layout}
                        data-id={actionsSlotTestId}
                    />
                )}

                {!isEmpty(keyValuePairs) && (
                    <Stack
                        width="100%"
                        gap="2xl"
                        align="center"
                        wrap="wrap"
                        gridArea="kvps"
                        data-id={keyValuePairsSlotTestId}
                    >
                        {keyValuePairs?.map(
                            ({
                                feedbackProps,
                                iconName,
                                iconSize,
                                id,
                                isInteractive,
                                label,
                                moreItemsText,
                                onClick,
                                truncationMode,
                                type,
                                value,
                                visibleItemsLimit,
                                ariaLabel,
                            }) => {
                                return (
                                    <KeyValuePair
                                        key={id}
                                        feedbackProps={feedbackProps}
                                        iconName={iconName}
                                        iconSize={iconSize}
                                        id={id}
                                        isInteractive={isInteractive}
                                        label={label}
                                        moreItemsText={moreItemsText}
                                        truncationMode={truncationMode}
                                        type={type}
                                        value={value}
                                        ariaLabel={ariaLabel}
                                        data-id={`${dataId}-${id}Kvp`}
                                        visibleItemsLimit={visibleItemsLimit}
                                        onClick={onClick}
                                    />
                                );
                            },
                        )}
                    </Stack>
                )}
                {banner !== undefined && (
                    <StyledGridAreaDiv
                        $gridArea="banner"
                        data-id={bannerSlotTestId}
                    >
                        {banner}
                    </StyledGridAreaDiv>
                )}
            </StyledPageHeaderWrapperDiv>
        </Stack>
    );
};
