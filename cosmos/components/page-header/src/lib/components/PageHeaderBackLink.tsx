import type { ReactNode } from 'react';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';

export interface PageHeaderBackLinkProps {
    backLink: ReactNode;
    'data-id'?: string;
}

export const PageHeaderBackLink = ({
    backLink,
    'data-id': dataId = undefined,
}: Readonly<PageHeaderBackLinkProps>): React.JSX.Element => {
    return (
        <Stack
            data-id={dataId}
            data-testid="PageHeaderBackLink"
            gap="xs"
            align="center"
            py="sm"
        >
            <Icon
                colorScheme="primary"
                size="100"
                name="ArrowLeft"
                data-id={`${dataId}-icon`}
            />
            {backLink}
        </Stack>
    );
};
