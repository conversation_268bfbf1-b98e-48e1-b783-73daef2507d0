import { useCallback, useLayoutEffect, useRef, useState } from 'react';

export type Layout = 'default' | 'stacked';

export function useResponsiveLayout(): {
    layout: Layout;
    resizer: (element: HTMLElement | null) => void;
} {
    const [layout, setLayout] = useState<Layout>('default');
    const observerRef = useRef<ResizeObserver | null>(null);
    const minWidth = 834;

    const resizer = useCallback(
        (element: HTMLElement | null) => {
            // Clean up previous observer
            if (observerRef.current) {
                observerRef.current.disconnect();
                observerRef.current = null;
            }

            if (element) {
                const observer = new ResizeObserver(([entry]) => {
                    setLayout(
                        entry.borderBoxSize[0].inlineSize < minWidth
                            ? 'stacked'
                            : 'default',
                    );
                });

                // ensures element is rendered
                requestAnimationFrame(() => {
                    observer.observe(element);
                    observerRef.current = observer;
                });
            }
        },
        [minWidth],
    );

    // Cleanup on unmount
    useLayoutEffect(() => {
        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, []);

    return { layout, resizer };
}
