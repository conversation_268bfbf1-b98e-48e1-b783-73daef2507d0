import { styled } from 'styled-components';
import {
    dimension2xl,
    dimension3xl,
    neutralBackgroundSurfaceInitial,
} from '@cosmos/constants/tokens';
import type { Layout } from '../hooks';

export const StyledPageHeaderWrapperDiv = styled.div<{
    $gridTemplate: string;
    $layout: Layout;
}>`
    background-color: ${neutralBackgroundSurfaceInitial};
    box-sizing: border-box;
    display: grid;
    justify-content: ${({ $layout }) =>
        $layout === 'stacked' ? 'stretch' : 'space-between'};
    grid-template-areas: ${({ $gridTemplate }) => $gridTemplate};

    padding: ${dimension3xl};
    width: 100%;
    gap: ${dimension2xl};
`;
