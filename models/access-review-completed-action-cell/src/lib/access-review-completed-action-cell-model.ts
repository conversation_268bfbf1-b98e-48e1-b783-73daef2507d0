import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import type {
    AccessReviewPeriodApplicationResponseDto,
    AccessReviewPeriodListResponseDto,
    UpdateReviewPeriodResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { AccessReviewRequestChangesModel } from '@models/access-review-request-changes';

/**
 * Represents the period data structure used in completed review actions.
 */
export interface CompletedReviewPeriodData {
    id: number;
    startDate: string;
    endDate: string;
    completedAt?: string;
}

/**
 * Model for handling business logic related to completed access review action cells.
 * Manages dropdown items creation, action availability, and modal configuration.
 */
export class AccessReviewCompletedActionCellModel {
    requestChangesModel = new AccessReviewRequestChangesModel();

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Creates a review period object from period data for request changes logic.
     * Assumes the period is completed if no completedAt is provided.
     * Returns the format expected by UpdateReviewPeriodResponseDto.
     */
    createReviewPeriodFromData(
        periodData?: CompletedReviewPeriodData,
    ): UpdateReviewPeriodResponseDto | undefined {
        if (!periodData) {
            return undefined;
        }

        return {
            id: periodData.id,
            startDate: periodData.startDate,
            endDate: periodData.endDate,
            completedAt: periodData.completedAt || new Date().toISOString(),
        } as UpdateReviewPeriodResponseDto;
    }

    /**
     * Determines the appropriate period ID for operations.
     * Uses reviewPeriodId from application if available, otherwise falls back to periodData or application id.
     */
    getPeriodId(
        application: AccessReviewPeriodApplicationResponseDto,
        periodData?: CompletedReviewPeriodData,
    ): number {
        return application.reviewPeriodId ?? periodData?.id ?? application.id;
    }

    /**
     * Creates the base dropdown items that are always available for completed reviews.
     */
    createBaseDropdownItems(
        applicationId: number,
        onDownloadEvidence: () => void,
    ): SchemaDropdownItems {
        return [
            {
                id: `${applicationId}-download-evidence`,
                label: t`Download evidence`,
                type: 'item',
                onClick: onDownloadEvidence,
            },
        ];
    }

    /**
     * Creates the request changes dropdown item.
     */
    createRequestChangesDropdownItem(
        applicationId: number,
        onRequestChanges: () => void,
    ): SchemaDropdownItems[0] {
        return {
            id: `${applicationId}-request-changes`,
            label: t`Request changes`,
            type: 'item',
            onClick: onRequestChanges,
        };
    }

    /**
     * Determines if request changes should be shown for the given application.
     */
    shouldShowRequestChanges(
        application: AccessReviewPeriodApplicationResponseDto,
        reviewPeriod: UpdateReviewPeriodResponseDto | undefined,
        activePeriodsData: AccessReviewPeriodListResponseDto | null,
        hasLimitedAccess: boolean,
    ): boolean {
        return this.requestChangesModel.canRequestChanges(
            application,
            reviewPeriod,
            activePeriodsData,
            hasLimitedAccess,
        );
    }

    /**
     * Creates the complete dropdown items array based on application state and permissions.
     */
    createDropdownItems(
        application: AccessReviewPeriodApplicationResponseDto,
        reviewPeriod: UpdateReviewPeriodResponseDto | undefined,
        activePeriodsData: AccessReviewPeriodListResponseDto | null,
        onDownloadEvidence: () => void,
        onRequestChanges: () => void,
        hasLimitedAccess: boolean,
    ): SchemaDropdownItems {
        const baseItems = this.createBaseDropdownItems(
            application.id,
            onDownloadEvidence,
        );

        const showRequestChanges = this.shouldShowRequestChanges(
            application,
            reviewPeriod,
            activePeriodsData,
            hasLimitedAccess,
        );

        if (showRequestChanges) {
            return [
                ...baseItems,
                this.createRequestChangesDropdownItem(
                    application.id,
                    onRequestChanges,
                ),
            ];
        }

        return baseItems;
    }

    /**
     * Creates the modal configuration for request changes.
     * Returns the configuration object without the content function to avoid React import issues.
     */
    createRequestChangesModalConfig(
        modalId: string,
        periodId: number,
        reviewPeriod: UpdateReviewPeriodResponseDto | undefined,
    ): {
        id: string;
        modalProps: {
            periodId: number;
            reviewPeriod: UpdateReviewPeriodResponseDto | undefined;
            'data-id': string;
        };
        centered: boolean;
        disableClickOutsideToClose: boolean;
        size: 'lg';
    } {
        return {
            id: modalId,
            modalProps: {
                periodId,
                reviewPeriod,
                'data-id': 'request-changes-completed-modal',
            },
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg' as const,
        };
    }

    /**
     * Generates a unique modal ID for request changes.
     */
    generateRequestChangesModalId(): string {
        return 'request-changes-completed-modal';
    }
}
