import { isEmpty } from 'lodash-es';
import { RESET_REQUIREMENTS_MODAL_DEFAULT_REQUIREMENTS_LINK } from '@components/controls';
import {
    sharedControlDetailsController,
    sharedControlPoliciesController,
    sharedControlsGetControlPolicyComparisonController,
    sharedControlsResetControlPolicyMappingsController,
} from '@controllers/controls';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';

export interface ShortPolicy {
    name: string;
    description?: string;
    templateId: number;
    isArchived: boolean;
    isReplaced: boolean;
}

interface PolicyComparisonResponse {
    alignedPolicies: ShortPolicy[];
    userMappedPolicies: ShortPolicy[];
    templateMappedPolicies: ShortPolicy[];
}

class ResetPoliciesModalModel {
    constructor() {
        makeAutoObservable(this);
    }

    get defaultPoliciesLink(): string {
        return RESET_REQUIREMENTS_MODAL_DEFAULT_REQUIREMENTS_LINK;
    }

    get isLoading(): boolean {
        return (
            sharedControlPoliciesController.isLoading ||
            sharedControlsGetControlPolicyComparisonController.isLoading
        );
    }

    get isSaving(): boolean {
        return sharedControlsResetControlPolicyMappingsController.isPending;
    }

    get hasDefaultPolicies(): boolean {
        return !isEmpty(this.policiesToAdd) || !isEmpty(this.policiesToRemove);
    }

    get policiesToAdd(): ShortPolicy[] {
        if (
            this.isLoading ||
            !sharedControlsGetControlPolicyComparisonController.controlPolicyComparison
        ) {
            return [];
        }

        return (
            sharedControlsGetControlPolicyComparisonController.controlPolicyComparison as unknown as PolicyComparisonResponse
        ).templateMappedPolicies;
    }

    get policiesToRemove(): ShortPolicy[] {
        if (
            this.isLoading ||
            !sharedControlsGetControlPolicyComparisonController.controlPolicyComparison
        ) {
            return [];
        }

        return (
            sharedControlsGetControlPolicyComparisonController.controlPolicyComparison as unknown as PolicyComparisonResponse
        ).userMappedPolicies;
    }

    get policiesToAddCount(): number {
        return this.policiesToAdd.length;
    }

    get policiesToRemoveCount(): number {
        return this.policiesToRemove.length;
    }

    loadPolicyComparison = (controlId: number): void => {
        sharedControlPoliciesController.load(controlId);

        sharedControlsGetControlPolicyComparisonController.load(controlId);
    };

    confirmResetPolicies = (): void => {
        const { controlId } = sharedControlDetailsController;

        if (!controlId) {
            return;
        }

        sharedControlsResetControlPolicyMappingsController.resetControlPolicyMappings(
            controlId,
        );

        when(
            () => !sharedControlsResetControlPolicyMappingsController.isPending,
            () => {
                if (
                    sharedControlsResetControlPolicyMappingsController.hasError
                ) {
                    snackbarController.addSnackbar({
                        id: 'reset-control-policies-error',
                        props: {
                            title: t`Default mappings not applied`,
                            description: t`Something went wrong. Try again or contact support if the issue continues.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'reset-control-policies-success',
                    props: {
                        title: t`Default mappings applied`,
                        description: t`This control now uses the standard policy mappings.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedControlPoliciesController.controlPoliciesQuery.invalidate();

                modalController.closeModal('policy-compare-to-defaults-modal');
            },
        );
    };
}

export const sharedResetPoliciesModalModel = new ResetPoliciesModalModel();
