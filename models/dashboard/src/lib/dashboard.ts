import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { makeAutoObservable } from '@globals/mobx';

class DashboardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isWorkspacesEnabled(): boolean {
        const { isMultipleWorkspacesEnabled } = sharedFeatureAccessModel;
        const { workspaces } = sharedCurrentCompanyController;
        const hasWorkspaceAccess =
            sharedCurrentUserController.hasUserPermission('Dashboard', 'READ');

        // TODO: Temporal fix
        const { isDevops } = sharedCurrentUserController;

        return (
            hasWorkspaceAccess &&
            !isDevops &&
            isMultipleWorkspacesEnabled &&
            workspaces.length >= 2
        );
    }
}

export const sharedDashboardModel = new DashboardModel();
