import type { DatatableRef } from '@cosmos/components/datatable';
import { makeAutoObservable } from '@globals/mobx';

class RequirementsModel {
    tableRef: React.RefObject<DatatableRef> | null = null;
    customFrameworkTableRef: React.RefObject<DatatableRef> | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    resetRowSelection = (): void => {
        if (this.tableRef) {
            this.tableRef.current?.resetRowSelection();
        }
    };
    resetCustomFrameworkRowSelection = (): void => {
        if (this.customFrameworkTableRef) {
            this.customFrameworkTableRef.current?.resetRowSelection();
        }
    };
}

export const sharedRequirementsModel = new RequirementsModel();
