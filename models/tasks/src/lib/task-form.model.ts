import { isEqual } from 'lodash-es';
import {
    CustomTaskCreateController,
    CustomTaskDataController,
    CustomTaskUpdateController,
    sharedCustomTasksController,
    type TaskGetParams,
} from '@controllers/tasks';
import { t } from '@globals/i18n/macro';
import {
    action,
    computed,
    makeAutoObservable,
    toJS,
    when,
} from '@globals/mobx';
import { getDueDateDayOfMonth } from './helpers/get-due-date-day-of-month.helper';
import { getDueDateWeekNumber } from './helpers/get-due-date-week-number.helper';
import { getDueDateWeekday } from './helpers/get-due-date-weekday.helper';
import { getDueDateWeekdayOption } from './helpers/get-due-date-weekday-option.helper';
import { getNextOccurrence } from './helpers/get-next-occurrence.helper';
import { mapTaskDataToFormValues } from './helpers/map-task-data-to-values.helper';
import {
    parseTaskFormValues,
    parseTaskSchedule,
} from './helpers/parse-task-form-values.helper';
import { getTaskFormDefaults } from './helpers/task-form-defaults.helper';
import { sharedTaskModalState } from './task-modal-state.model';
import type { TaskFormValues } from './types/task-form-values.type';

class TaskFormModel {
    params: TaskGetParams = {};
    initialValues: TaskFormValues = getTaskFormDefaults();
    currentValues: TaskFormValues = getTaskFormDefaults();
    constructor() {
        makeAutoObservable(this, {
            init: action,
            isEdit: computed,
            isDirty: computed,
            modalFormTitle: computed,
            canMapControl: computed,
            canMapRisk: computed,
            isRiskManagementEnabled: computed,
            submit: action,
            setValue: action,
            update: action,
            onChange: action,
            updateDueDate: action,
            nextOccurrence: computed,
        });
    }

    async init(params: TaskGetParams): Promise<void> {
        // reset form data
        this.params = params;
        this.initialValues = getTaskFormDefaults();
        this.currentValues = getTaskFormDefaults();

        // load task data if taskId is provided
        const taskDataController = new CustomTaskDataController();

        taskDataController.load(params);
        await when(() => !taskDataController.isLoading);
        const { taskData } = taskDataController;

        if (!taskData) {
            return;
        }

        // map task data to form values
        this.initialValues = mapTaskDataToFormValues(taskData);
        this.currentValues = mapTaskDataToFormValues(taskData);
    }

    setValue = <Key extends keyof TaskFormValues>(
        key: Key,
        value: TaskFormValues[Key],
    ): void => {
        this.currentValues[key] = value;
    };

    update =
        <Key extends keyof TaskFormValues>(key: Key) =>
        (value: TaskFormValues[Key]) => {
            this.setValue(key, value);
        };

    onChange =
        <Key extends keyof TaskFormValues>(key: Key) =>
        (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
            this.update(key)(e.currentTarget.value as TaskFormValues[Key]);
        };

    get isEdit(): boolean {
        return Boolean(this.params.taskId);
    }

    get isDirty(): boolean {
        return !isEqual(this.initialValues, this.currentValues);
    }

    get modalFormTitle(): string {
        return this.isEdit ? t`Edit task` : t`Create task`;
    }

    get canMapControl(): boolean {
        /** TODO: replace with RBAC rules. */
        return true;
    }

    get canMapRisk(): boolean {
        /** TODO: replace with RBAC rules. */
        return true;
    }

    get isRiskManagementEnabled(): boolean {
        /** TODO: replace with launch darkly check. */
        return true;
    }

    submit = async (): Promise<void> => {
        // parse data
        const data = parseTaskFormValues(this.currentValues);

        // save task
        if (this.isEdit) {
            await new CustomTaskUpdateController().saveTask(this.params, data);
        } else {
            await new CustomTaskCreateController().saveTask(data);
        }

        // reset states and close modal
        sharedTaskModalState.closeModal();
        sharedCustomTasksController.loadAll();
    };

    updateDueDate = (dueDate = '') => {
        this.update('dueDate')(dueDate);
        if (!this.currentValues.scheduleDaysOfWeek?.length) {
            const scheduleDaysOfWeek = getDueDateWeekday(dueDate);

            if (scheduleDaysOfWeek) {
                this.update('scheduleDaysOfWeek')([scheduleDaysOfWeek]);
            }
        }
        if (!this.currentValues.scheduleDayOfMonth) {
            const scheduleDayOfMonth = getDueDateDayOfMonth(dueDate);

            if (scheduleDayOfMonth) {
                this.update('scheduleDayOfMonth')(scheduleDayOfMonth);
            }
        }
        if (!this.currentValues.scheduleWeekNumber) {
            const scheduleWeekNumber = getDueDateWeekNumber(dueDate);

            if (scheduleWeekNumber) {
                this.update('scheduleWeekNumber')(scheduleWeekNumber);
            }
        }
        if (!this.currentValues.scheduleWeekday) {
            const scheduleWeekday = getDueDateWeekdayOption(dueDate);

            if (scheduleWeekday) {
                this.update('scheduleWeekday')(scheduleWeekday);
            }
        }
    };

    get nextOccurrence(): string {
        const schedule = parseTaskSchedule(toJS(this.currentValues));

        return getNextOccurrence(schedule);
    }
}

export const sharedTaskForm = new TaskFormModel();
