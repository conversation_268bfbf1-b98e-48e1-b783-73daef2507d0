import type { TaskScheduleWeekNumber } from '../types/task-form-values.type';
import { getTaskCardinalOptions } from './task-options.helper';

export const getDueDateWeekNumber = (
    dueDate: string,
): TaskScheduleWeekNumber | undefined => {
    const [year, month, day] = dueDate.split('-');
    const selectedDate = Number(day);

    if (Number.isNaN(selectedDate)) {
        return;
    }

    // get which week of the month it is
    const firstDayOfMonth = new Date(Number(year), Number(month) - 1, 1);
    const firstWeekday = firstDayOfMonth.getDay();
    const weekNumber = Math.ceil((selectedDate + firstWeekday) / 7);

    if (Number.isNaN(weekNumber)) {
        return;
    }

    return getTaskCardinalOptions().find(
        (option) => option.value === String(weekNumber),
    );
};
