import {
    type RecurringScheduleSpec,
    RecurringType,
    type Weekday,
} from '@drata/recurring-schedule';
import type { CustomTaskBaseRequestDto } from '@globals/api-sdk/types';
import type { TaskFormValues } from '@models/tasks';

/**
 * Get entity ID based on the mapped type and selected entity.
 */
const getEntityId = (values: TaskFormValues): number | null => {
    const { entityId, taskType, control, risk } = values;

    if (entityId) {
        return entityId;
    }

    if (taskType === 'CONTROL' && control?.id) {
        return parseInt(control.id, 10);
    }
    if (taskType === 'RISK' && risk?.id) {
        return parseInt(risk.id, 10);
    }

    return null;
};

export const parseTaskSchedule = (
    values: TaskFormValues,
): RecurringScheduleSpec | undefined => {
    const {
        dueDate,
        isRecurringTask,
        scheduleType,
        scheduleMonthlyType,
        scheduleDaysOfWeek,
        scheduleDayOfMonth,
        scheduleEndsOn,
        scheduleEnd,
        scheduleInterval,
        scheduleWeekNumber,
        scheduleWeekday,
    } = values;

    if (!isRecurringTask || !scheduleType || !dueDate) {
        return;
    }

    const start = dueDate;
    const end = scheduleEndsOn === 'ON' ? scheduleEnd : undefined;
    const interval = scheduleInterval ? Number(scheduleInterval) : 1;

    if (
        scheduleType.value === RecurringType.WEEKLY &&
        scheduleDaysOfWeek?.length
    ) {
        return {
            start,
            type: RecurringType.WEEKLY,
            interval,
            end,
            daysOfWeek: scheduleDaysOfWeek,
        };
    }

    if (
        scheduleType.value === RecurringType.MONTHLY &&
        scheduleMonthlyType === 'specificDayOfMonth' &&
        scheduleDayOfMonth
    ) {
        return {
            start,
            type: RecurringType.MONTHLY,
            dayOfMonth: Number(scheduleDayOfMonth.value),
            interval,
            end,
        };
    }

    if (
        scheduleType.value === RecurringType.MONTHLY &&
        scheduleMonthlyType === 'specificDayOfWeek' &&
        scheduleWeekNumber &&
        scheduleWeekday
    ) {
        return {
            start,
            type: RecurringType.MONTHLY,
            weekNumber: Number(scheduleWeekNumber.value),
            weekday: scheduleWeekday.value as Weekday,
            interval,
            end,
        };
    }

    if (scheduleType.value === RecurringType.YEARLY) {
        const [, monthPart, dayPart] = start.split('-');
        const month = Number(monthPart);
        const day = Number(dayPart);

        if (Number.isNaN(month) || Number.isNaN(day)) {
            return undefined;
        }

        return {
            type: RecurringType.YEARLY,
            interval,
            month,
            day,
            start,
            end,
        };
    }

    return undefined;
};

export const parseTaskFormValues = (
    values: TaskFormValues,
): CustomTaskBaseRequestDto => {
    const entityId = getEntityId(values);

    return {
        title: values.title ?? '',
        description: values.description || null,
        dueDate: values.dueDate ?? '',
        assigneeId: parseInt(values.assignee?.id ?? '', 10),
        taskType: values.taskType || 'GENERAL',
        entityId,
    };
};
