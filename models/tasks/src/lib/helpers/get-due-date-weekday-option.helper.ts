import type { TaskScheduleWeekday } from '../types/task-form-values.type';
import { getTasksWeekdaysOptions } from './task-options.helper';

export const getDueDateWeekdayOption = (
    dueDate: string,
): TaskScheduleWeekday | undefined => {
    const [year, month, day] = dueDate.split('-');
    const selectedDate = new Date(Number(year), Number(month) - 1, Number(day));

    if (Number.isNaN(selectedDate.getTime())) {
        return;
    }

    const selectedDay = selectedDate
        .toLocaleDateString('en-US', {
            weekday: 'long',
        })
        .toUpperCase();

    const options = getTasksWeekdaysOptions();

    return options.find((option) => option.id === selectedDay);
};
