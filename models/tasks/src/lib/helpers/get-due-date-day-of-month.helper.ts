import type { ListBoxItemData } from '@cosmos/components/list-box';
import { getDayOptions } from './get-day-options.helper';

export const getDueDateDayOfMonth = (
    dueDate: string,
): ListBoxItemData | undefined => {
    const [, , day] = dueDate.split('-');

    const selectedDate = Number(day);

    if (Number.isNaN(selectedDate)) {
        return;
    }

    return getDayOptions().find(
        (option) => option.value === String(selectedDate),
    );
};
