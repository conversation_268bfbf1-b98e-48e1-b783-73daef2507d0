import type { Weekday } from '@drata/recurring-schedule';
import { getTaskShortWeekdaysOptions } from './task-options.helper';

export const getDueDateWeekday = (dueDate: string): Weekday | undefined => {
    const [year, month, day] = dueDate.split('-');
    const selectedDate = new Date(Number(year), Number(month) - 1, Number(day));

    if (Number.isNaN(selectedDate.getTime())) {
        return;
    }

    const selectedDay = selectedDate
        .toLocaleDateString('en-US', {
            weekday: 'long',
        })
        .toUpperCase()
        .slice(0, 2) as Weekday;

    const options = getTaskShortWeekdaysOptions();

    return options.find((option) => option.value === selectedDay)?.value;
};
