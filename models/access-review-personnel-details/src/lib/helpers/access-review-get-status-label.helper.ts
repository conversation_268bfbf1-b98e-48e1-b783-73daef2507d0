import type { AccessReviewPeriodApplicationUserResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getStatusLabel = (
    status: AccessReviewPeriodApplicationUserResponseDto['status'],
): string => {
    switch (status) {
        case 'APPROVED': {
            return t`Approved`;
        }
        case 'REJECTED': {
            return t`Rejected`;
        }
        case 'OUT_OF_SCOPE': {
            return t`Out of scope`;
        }
        case 'NOT_REVIEWED':
        default: {
            return t`Not reviewed`;
        }
    }
};
