import { isEmpty } from 'lodash-es';
import { useState } from 'react';
import { sharedAccessReviewBulkActionStatusController } from '@controllers/access-reviews';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextareaField } from '@cosmos/components/textarea-field';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

const FORM_ID = 'access-review-reject-personnel-status-note-form';
const MAX_CHARACTERS = 191;

interface AccessReviewUpdatePersonnelStatusModalProps {
    userId: number;
    userFullName: string;
    onClose: () => void;
}

export const AccessReviewUpdatePersonnelStatusModal = observer(
    ({
        userId,
        userFullName,
        onClose,
    }: AccessReviewUpdatePersonnelStatusModalProps): React.JSX.Element => {
        const [note, setNote] = useState<string>('');
        const [feedback, setFeedback] = useState<
            { type: 'error' | 'success'; message: string } | undefined
        >(undefined);

        const { updateStatus } = sharedAccessReviewBulkActionStatusController;

        const handleOnChangeNote = (
            event: React.ChangeEvent<HTMLTextAreaElement>,
        ) => {
            setNote(event.target.value);
            setFeedback(undefined);
        };

        const handleSave = () => {
            if (isEmpty(note)) {
                setFeedback({
                    type: 'error',
                    message: t`Note is required.`,
                });

                return;
            }

            if (note.length > MAX_CHARACTERS) {
                setFeedback({
                    type: 'error',
                    message: t`Note cannot be longer than 191 characters.`,
                });

                return;
            }

            updateStatus([userId], 'REJECTED', note);

            onClose();
        };

        return (
            <Modal size="lg" data-id="j7zAJBEs">
                <Modal.Header
                    size="md"
                    title={t`Change review status for ${userFullName}`}
                    closeButtonAriaLabel="Close reject note modal"
                    onClose={onClose}
                />
                <Modal.Body size="md">
                    <Stack gap="4x" direction="column">
                        <Text size="200">
                            <Trans>
                                Please tell us why the selected personnel’s
                                review status is rejected.
                            </Trans>
                        </Text>
                        <TextareaField
                            feedback={feedback}
                            formId={FORM_ID}
                            helpText={t`The reason for rejection will be displayed in the internal notes section.`}
                            name="note"
                            label={t`Reason for rejection`}
                            value={note}
                            maxCharacters={MAX_CHARACTERS}
                            onChange={handleOnChangeNote}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    size="md"
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: () => {
                                onClose();
                            },
                        },
                        {
                            label: t`Save`,
                            onClick: handleSave,
                        },
                    ]}
                />
            </Modal>
        );
    },
);
